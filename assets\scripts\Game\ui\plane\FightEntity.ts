import { _decorator } from 'cc';
const { ccclass } = _decorator;

import Entity from 'db://assets/scripts/Game/ui/base/Entity';
import SkillComp from './skill/SkillComp';
import BuffComp, { Buff } from './skill/BuffComp';
import { builtin, res } from '../../../AutoGen/Luban/schema';


@ccclass('FightEntity')
export default class FightEntity extends Entity {
    enemy = true; // 是否为敌机
    isDead = false; // 是否死亡
    type = 0; // 敌人类型

    maxHp:number = 0;
    curHp:number = 0;
    attack:number = 0;

    private _skillComp: SkillComp|null = null;
    private _buffComp: BuffComp|null = null;
    
    init() {
        this._skillComp = new SkillComp();
        this.addComp("skill", this._skillComp);
        this._buffComp = new BuffComp();
        this.addComp("buff", this._buffComp)
        super.init();
    }

    get skillComp() {
        return this._skillComp!;
    }
    
    get buffComp() {
        return this._buffComp!;
    }

    CastSkill(skillID: number) {
        this.skillComp.Cast(this, skillID);
    }

    toDie():boolean {
        if (this.isDead) {
            return false
        }
        this.isDead = true;
        return true
    }

    hurt(damage: number) {
    }
    
    ApplyBuffEffect(buff: Buff|null, effectData: builtin.EffectParam) {
        switch(effectData.type) {
            case res.EffectType.Kill:
                this.toDie();
                break;
            case res.EffectType.Hurt:
                if (effectData.param.length >= 1)
                {
                    this.hurt(effectData.param[0]);
                }
                break;
            default:
                break;
        }
    }
    RemoveBuffEffect(buff: Buff, effectData: builtin.EffectParam) {
        switch(effectData.type) {
            default:
                break;
        }
    }
}