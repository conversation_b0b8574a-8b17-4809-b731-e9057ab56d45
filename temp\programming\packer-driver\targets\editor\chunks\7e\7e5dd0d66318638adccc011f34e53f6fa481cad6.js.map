{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts"], "names": ["_decorator", "instantiate", "Component", "assetManager", "EDITOR", "BulletSystem", "ccclass", "playOnFocus", "executeInEditMode", "property", "disallowMultiple", "menu", "EmitterEditor", "visible", "displayName", "_timeAccumulator", "_updateInEditor", "targetFrameRate", "fixedDelta", "value", "frameTime", "frameTimeInMilliseconds", "emitterCount", "allEmitters", "length", "bulletCount", "allBullets", "eventGroupCount", "allEventGroups", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "start", "reset", "frameCount", "destroyAllBullets", "emitter", "console", "log", "update", "dt", "milli_dt", "tick", "instantiatePrefab", "prefabUuid", "loadAny", "uuid", "err", "prefab", "node", "parent", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,W,OAAAA,W;AAA2BC,MAAAA,S,OAAAA,S;AAAgCC,MAAAA,Y,OAAAA,Y;;AAC7EC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,WAAX;AAAwBC,QAAAA,iBAAxB;AAA2CC,QAAAA,QAA3C;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA;AAAvE,O,GAAiFX,U;;+BAO1EY,a,WALZN,OAAO,CAAC,eAAD,C,UACPK,IAAI,CAAC,aAAD,C,UACJJ,WAAW,CAAC,IAAD,C,UACXC,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAEZD,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAGRJ,QAAQ,CAAC;AAACK,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UASRL,QAAQ,CAAC;AAACK,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAKRL,QAAQ,CAAC;AAACK,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAKRL,QAAQ,CAAC;AAACK,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAKRL,QAAQ,CAAC;AAACK,QAAAA,WAAW,EAAE;AAAd,OAAD,C,kGAjCb,MAKaF,aALb,SAKmCV,SALnC,CAK6C;AAAA;AAAA;;AAAA;;AAAA,eAmCjCa,gBAnCiC,GAmCN,CAnCM;AAAA,eAoCjCC,eApCiC,GAoCN,KApCM;AAAA;;AAEd;AAGD,YAAfC,eAAe,GAAW;AACjC,iBAAO,OAAO,KAAKC,UAAnB;AACH;;AAEyB,YAAfD,eAAe,CAACE,KAAD,EAAgB;AACtC,eAAKD,UAAL,GAAkB,OAAOC,KAAzB;AACH;;AAGmB,YAATC,SAAS,GAAW;AAC3B,iBAAOR,aAAa,CAACS,uBAArB;AACH;;AAGsB,YAAZC,YAAY,GAAW;AAC9B,iBAAO;AAAA;AAAA,4CAAaC,WAAb,CAAyBC,MAAhC;AACH;;AAGqB,YAAXC,WAAW,GAAW;AAC7B,iBAAO;AAAA;AAAA,4CAAaC,UAAb,CAAwBF,MAA/B;AACH;;AAGyB,YAAfG,eAAe,GAAW;AACjC,iBAAO;AAAA;AAAA,4CAAaC,cAAb,CAA4BJ,MAAnC;AACH;;AAODK,QAAAA,aAAa,GAAG;AACZ,eAAKb,eAAL,GAAuB,IAAvB,CADY,CAEZ;AACH;;AAEDc,QAAAA,eAAe,GAAG;AACd,eAAKd,eAAL,GAAuB,IAAvB,CADc,CAEd;AAEA;AACA;AACH;;AAEDe,QAAAA,mBAAmB,GAAS;AACxB,eAAKf,eAAL,GAAuB,KAAvB,CADwB,CAExB;AACH;;AAEDgB,QAAAA,KAAK,GAAG;AACJ,eAAKC,KAAL;AACH;;AAEDA,QAAAA,KAAK,GAAG;AACJrB,UAAAA,aAAa,CAACsB,UAAd,GAA2B,CAA3B;AACAtB,UAAAA,aAAa,CAACS,uBAAd,GAAwC,CAAxC;AACA,eAAKN,gBAAL,GAAwB,CAAxB;AACA;AAAA;AAAA,4CAAaoB,iBAAb,CAA+B,IAA/B;;AACA,eAAK,MAAMC,OAAX,IAAsB;AAAA;AAAA,4CAAab,WAAnC,EAAgD;AAC5Ca,YAAAA,OAAO,CAACP,aAAR;AACH;;AACD;AAAA;AAAA,4CAAaD,cAAb,GAA8B,EAA9B;AACAS,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAKtB,eAA5B;AACH;;AAEDuB,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,cAAIpC,MAAM,IAAI,KAAKY,eAAnB,EAAoC;AAChC;AAEA;AACA;AACA;AACA,kBAAMyB,QAAQ,GAAGD,EAAE,GAAG,IAAtB,CANgC,CAOhC;AACA;;AACI5B,YAAAA,aAAa,CAACsB,UAAd,IAA4B,CAA5B;AACAtB,YAAAA,aAAa,CAACS,uBAAd,IAAyCoB,QAAzC;AACA;AAAA;AAAA,8CAAaC,IAAb,CAAkBD,QAAlB,EAX4B,CAY5B;AACA;AACJ;AACH;AACJ,SAzFwC,CA2FzC;;;AACOE,QAAAA,iBAAiB,CAACC,UAAD,EAAqB;AACzC;AACA;AACAzC,UAAAA,YAAY,CAAC0C,OAAb,CAAqB;AAACC,YAAAA,IAAI,EAAEF;AAAP,WAArB,EAAyC,CAACG,GAAD,EAAMC,MAAN,KAAiB;AACtD,gBAAID,GAAJ,EAAS;AACLV,cAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsCS,GAAtC;AACA;AACH;;AACD,kBAAME,IAAI,GAAGhD,WAAW,CAAC+C,MAAD,CAAxB;AACA,kBAAME,MAAM,GAAG,KAAKD,IAApB;AACAC,YAAAA,MAAM,CAAEC,QAAR,CAAiBF,IAAjB;AACH,WARD;AASH,SAxGwC,CA0GzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA1HyC,O,UAiClCf,U,GAAqB,C,UACrBb,uB,GAAkC,C;;;;;iBAhCrB,K", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Prefab, Component, <PERSON><PERSON><PERSON><PERSON>, director, assetManager } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletSystem } from '../../scripts/Game/bullet/BulletSystem';\r\nconst { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu  } = _decorator;\r\n\r\n@ccclass('EmitterEditor')\r\n@menu('子弹系统/发射器编辑器')\r\n@playOnFocus(true)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class EmitterEditor extends Component {\r\n    @property({visible:false})\r\n    fixedDelta:number = 16.67; // Fixed time step (e.g., 60 FPS), 单位: 毫秒\r\n\r\n    @property({displayName: \"目标帧率\"})\r\n    public get targetFrameRate(): number {\r\n        return 1000 / this.fixedDelta;\r\n    }\r\n\r\n    public set targetFrameRate(value: number) {\r\n        this.fixedDelta = 1000 / value;\r\n    }\r\n\r\n    @property({displayName: \"当前时间(ms)\"})\r\n    public get frameTime(): number {\r\n        return EmitterEditor.frameTimeInMilliseconds;\r\n    }\r\n\r\n    @property({displayName: \"当前发射器数量\"})\r\n    public get emitterCount(): number {\r\n        return BulletSystem.allEmitters.length;\r\n    }\r\n\r\n    @property({displayName: \"当前子弹数量\"})\r\n    public get bulletCount(): number {\r\n        return BulletSystem.allBullets.length;\r\n    }\r\n\r\n    @property({displayName: \"当前事件组数量\"})\r\n    public get eventGroupCount(): number {\r\n        return BulletSystem.allEventGroups.length;\r\n    }\r\n\r\n    static frameCount: number = 0;\r\n    static frameTimeInMilliseconds: number = 0;\r\n    private _timeAccumulator: number = 0;\r\n    private _updateInEditor: boolean = false;\r\n\r\n    resetInEditor() {\r\n        this._updateInEditor = true;\r\n        // console.log('resetInEditor');\r\n    }\r\n\r\n    onFocusInEditor() {\r\n        this._updateInEditor = true;\r\n        // console.log('onFocusInEditor');\r\n        \r\n        // @ts-ignore\r\n        // Editor.Selection.select('node', BulletSystem.allEmitters.map(emitter => emitter.node.uuid));\r\n    }\r\n\r\n    onLostFocusInEditor(): void {\r\n        this._updateInEditor = false;\r\n        // this.reset();\r\n    }\r\n\r\n    start() {\r\n        this.reset();\r\n    }\r\n\r\n    reset() {\r\n        EmitterEditor.frameCount = 0;\r\n        EmitterEditor.frameTimeInMilliseconds = 0;\r\n        this._timeAccumulator = 0;\r\n        BulletSystem.destroyAllBullets(true);                \r\n        for (const emitter of BulletSystem.allEmitters) {\r\n            emitter.resetInEditor();\r\n        }\r\n        BulletSystem.allEventGroups = [];\r\n        console.log('reset: ', this._updateInEditor);\r\n    }\r\n\r\n    update(dt: number) {\r\n        if (EDITOR && this._updateInEditor) {\r\n            // this._timeAccumulator += dt * 1000;\r\n\r\n            // if (this._timeAccumulator > 250) {\r\n            //     this._timeAccumulator = 250;\r\n            // }\r\n            const milli_dt = dt * 1000;\r\n            // console.log('Time Accumulator:', this._timeAccumulator, ', Fixed Delta:', this.fixedDelta, ', dt: ', dt);\r\n            // while (this._timeAccumulator >= this.fixedDelta) {\r\n                EmitterEditor.frameCount += 1;\r\n                EmitterEditor.frameTimeInMilliseconds += milli_dt;\r\n                BulletSystem.tick(milli_dt);\r\n                // this._timeAccumulator -= this.fixedDelta;\r\n                // console.log(`tick all at frame ${EmitterEditor.frameCount}`);\r\n            // }\r\n        }\r\n    }\r\n\r\n    // 编辑器方法\r\n    public instantiatePrefab(prefabUuid: string) {\r\n        // replace db://assets/resources/Game/prefabs/emitter/ with assets/resources/Game/prefabs/emitter/\r\n        //prefabUrl = prefabUrl.replace('db://', '');\r\n        assetManager.loadAny({uuid: prefabUuid}, (err, prefab) => {\r\n            if (err) {\r\n                console.log('Failed to load prefab:', err);\r\n                return;\r\n            }\r\n            const node = instantiate(prefab!);\r\n            const parent = this.node;\r\n            parent!.addChild(node);\r\n        });\r\n    }\r\n\r\n    // public saveToPrefab(nodeUuid: string, prefabUrl: string): Promise<string> {\r\n    //     console.log('saveToPrefab in Component:', nodeUuid, prefabUrl);        \r\n    //     return new Promise<string>((resolve, reject) => {\r\n    //         const scene = director.getScene();\r\n    //         const target = scene!.getChildByUuid(nodeUuid);\r\n    //         if (!target) {\r\n    //             console.error(\"node not found:\", nodeUuid);\r\n    //             reject();\r\n    //             return;\r\n    //         }\r\n    //         const json = cce.Utils.serialize(target);\r\n    //         // 将节点保存为 Prefab\r\n    //         // _utils.applyTargetOverrides(target as Node);\r\n    //         // Editor.Message.request('asset-db', 'save-asset', prefabUrl, json);\r\n    //         resolve(json);\r\n    //     });\r\n    // }\r\n}\r\n"]}