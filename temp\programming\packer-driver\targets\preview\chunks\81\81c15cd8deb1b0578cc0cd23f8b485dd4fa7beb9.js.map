{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts"], "names": ["_decorator", "misc", "assetManager", "EDITOR", "BulletProperty", "BulletController", "EmitterData", "BulletData", "ObjectPool", "BulletSystem", "EventGroupContext", "PropertyContainerComponent", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "eEmitterProp", "Emitter", "displayName", "type", "isActive", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitBulletID", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "totalElapsedTime", "bulletProp", "eventGroups", "_status", "None", "_statusElapsedTime", "_isEmitting", "_nextEmitTime", "_bulletPrefab", "_prewarmEffectPrefab", "_emitEffectPrefab", "_perEmitBulletQueue", "isEmitting", "status", "statusElapsedTime", "onLoad", "createProperties", "createEventGroups", "onEnable", "onCreateEmitter", "onDisable", "onDestroyEmitter", "onLostFocusInEditor", "updatePropertiesInEditor", "resetInEditor", "resetProperties", "changeStatus", "value", "emitterData", "bulletID", "eval", "notifyAll", "clear", "addProperty", "IsActive", "TotalElapsedTime", "EmitBulletID", "IsOnlyInScreen", "IsPreWarm", "IsLoop", "InitialDelay", "PreWarmDuration", "EmitDuration", "EmitInterval", "EmitPower", "LoopInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "Arc", "<PERSON><PERSON>", "on", "eventGroupData", "length", "ctx", "emitter", "eventGroup", "createEmitterEventGroup", "resetFromData", "bulletData", "Prewarm", "Emitting", "for<PERSON>ach", "group", "start", "Loop<PERSON>ndReached", "stop", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "j", "targetTime", "i", "push", "index", "perEmitIndex", "emitSingle", "processPerEmitQueue", "nextBullet", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "getEmitOffsetX", "interval", "stepsFromMiddle", "floor", "ceil", "createBulletInEditor", "console", "warn", "bullet", "instantiateBullet", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onCreateBullet", "prop", "speedAngle", "atan2", "speed", "resetEventGroups", "prefabPath", "Editor", "Message", "request", "then", "uuid", "loadAny", "err", "prefab", "error", "bulletNode", "getNode", "bulletParent", "getComponent", "destroy", "name", "kBulletNameInEditor", "playEffect", "rotation", "duration", "effectNode", "setWorldRotation", "scheduleOnce", "returnNode", "isInScreen", "tick", "deltaTime", "updateStatusNone", "updateStatusPrewarm", "updateStatusEmitting", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "wasEmitting", "notify"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAA+DC,MAAAA,Y,OAAAA,Y;;AAC3EC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,gB,iBAAAA,gB;;AAChBC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,0B,iBAAAA,0B;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA;AAA1D,O,GAAoEhB,U;OACpE;AAAEiB,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCjB,I;;gCAEnCkB,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;;yBAaCC,O,WALZT,OAAO,CAAC,SAAD,C,UAEPI,IAAI,CAAC,UAAD,C,UACJH,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAKZD,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAGRR,QAAQ,CAAC;AAACS,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBD,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,UAGRR,QAAQ,CAAC;AAACS,QAAAA,IAAI;AAAA;AAAA,oCAAL;AAAmBD,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,mFAfb,MAKaD,OALb;AAAA;AAAA,oEAKsE;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAalE;AAbkE,eAc3DG,QAd2D;AAAA,eAe3DC,cAf2D;AAAA,eAgB3DC,SAhB2D;AAAA,eAiB3DC,MAjB2D;AAAA,eAkB3DC,YAlB2D;AAAA,eAmB3DC,eAnB2D;AAAA,eAoB3DC,YApB2D;AAAA,eAqB3DC,YArB2D;AAAA,eAsB3DC,YAtB2D;AAAA,eAuB3DC,SAvB2D;AAAA,eAwB3DC,YAxB2D;AAAA,eAyB3DC,YAzB2D;AAAA,eA0B3DC,eA1B2D;AAAA,eA2B3DC,cA3B2D;AAAA,eA4B3DC,KA5B2D;AAAA,eA6B3DC,KA7B2D;AAAA,eA8B3DC,GA9B2D;AAAA,eA+B3DC,MA/B2D;AAAA,eAgC3DC,gBAhC2D;AAiClE;AAjCkE,eAkC3DC,UAlC2D;AAoClE;AApCkE,eAqC3DC,WArC2D,GAqC/B,EArC+B;AAuClE;AAvCkE,eAwCxDC,OAxCwD,GAwC9B1B,cAAc,CAAC2B,IAxCe;AAAA,eAyCxDC,kBAzCwD,GAyC3B,CAzC2B;AAAA,eA0CxDC,WA1CwD,GA0CjC,KA1CiC;AAAA,eA2CxDC,aA3CwD,GA2ChC,CA3CgC;AAAA,eA4CxDC,aA5CwD,GA4C3B,IA5C2B;AAAA,eA6CxDC,oBA7CwD,GA6CpB,IA7CoB;AAAA,eA8CxDC,iBA9CwD,GA8CvB,IA9CuB;AAgDlE;AAhDkE,eAiDxDC,mBAjDwD,GAiDgC,EAjDhC;AAAA;;AAmDpD,YAAVC,UAAU,GAAY;AAAE,iBAAO,KAAKN,WAAZ;AAA0B;;AAC5C,YAANO,MAAM,GAAmB;AAAE,iBAAO,KAAKV,OAAZ;AAAsB;;AAChC,YAAjBW,iBAAiB,GAAW;AAAE,iBAAO,KAAKT,kBAAZ;AAAiC;;AAEzDU,QAAAA,MAAM,GAAU;AACtB,eAAKC,gBAAL;AACA,eAAKC,iBAAL;AACH;;AAESC,QAAAA,QAAQ,GAAS;AACvB;AAAA;AAAA,4CAAaC,eAAb,CAA6B,IAA7B;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaC,gBAAb,CAA8B,IAA9B;AACH;;AAEMC,QAAAA,mBAAmB,GAAS;AAC/B,eAAKC,wBAAL;AACA,eAAKN,iBAAL;AACH;;AAEMO,QAAAA,aAAa,GAAG;AACnB,eAAKC,eAAL;AACA,eAAKC,YAAL,CAAkBjD,cAAc,CAAC2B,IAAjC;AACA,eAAKE,WAAL,GAAmB,KAAnB;AACA,eAAKN,gBAAL,CAAsB2B,KAAtB,GAA8B,CAA9B;AACH;;AAEMJ,QAAAA,wBAAwB,GAAG;AAC9B,cAAI,CAAC,KAAKK,WAAV,EAAuB;AAEvB,eAAK9C,QAAL,CAAc6C,KAAd,GAAsB,IAAtB;AACA,eAAKvC,YAAL,CAAkBuC,KAAlB,GAA0B,KAAKE,QAA/B;AACA,eAAK9C,cAAL,CAAoB4C,KAApB,GAA4B,KAAKC,WAAL,CAAiB7C,cAA7C;AACA,eAAKC,SAAL,CAAe2C,KAAf,GAAuB,KAAKC,WAAL,CAAiB5C,SAAxC;AACA,eAAKC,MAAL,CAAY0C,KAAZ,GAAoB,KAAKC,WAAL,CAAiB3C,MAArC;AAEA,eAAKC,YAAL,CAAkByC,KAAlB,GAA0B,KAAKC,WAAL,CAAiB1C,YAAjB,CAA8B4C,IAA9B,EAA1B;AACA,eAAK3C,eAAL,CAAqBwC,KAArB,GAA6B,KAAKC,WAAL,CAAiBzC,eAAjB,CAAiC2C,IAAjC,EAA7B;AACA,eAAKzC,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBvC,YAAjB,CAA8ByC,IAA9B,EAA1B;AACA,eAAKxC,YAAL,CAAkBqC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBtC,YAAjB,CAA8BwC,IAA9B,EAA1B;AACA,eAAKvC,SAAL,CAAeoC,KAAf,GAAuB,KAAKC,WAAL,CAAiBrC,SAAjB,CAA2BuC,IAA3B,EAAvB;AACA,eAAKtC,YAAL,CAAkBmC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBpC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA,eAAKrC,YAAL,CAAkBkC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBnC,YAAjB,CAA8BqC,IAA9B,EAA1B;AACA,eAAKpC,eAAL,CAAqBiC,KAArB,GAA6B,KAAKC,WAAL,CAAiBlC,eAAjB,CAAiCoC,IAAjC,EAA7B;AACA,eAAKnC,cAAL,CAAoBgC,KAApB,GAA4B,KAAKC,WAAL,CAAiBjC,cAAjB,CAAgCmC,IAAhC,EAA5B;AACA,eAAKlC,KAAL,CAAW+B,KAAX,GAAmB,KAAKC,WAAL,CAAiBhC,KAAjB,CAAuBkC,IAAvB,EAAnB;AACA,eAAKjC,KAAL,CAAW8B,KAAX,GAAmB,KAAKC,WAAL,CAAiB/B,KAAjB,CAAuBiC,IAAvB,EAAnB;AACA,eAAKhC,GAAL,CAAS6B,KAAT,GAAiB,KAAKC,WAAL,CAAiB9B,GAAjB,CAAqBgC,IAArB,EAAjB;AACA,eAAK/B,MAAL,CAAY4B,KAAZ,GAAoB,KAAKC,WAAL,CAAiB7B,MAAjB,CAAwB+B,IAAxB,EAApB;AAEA,eAAKC,SAAL,CAAe,IAAf;AACH;;AAESf,QAAAA,gBAAgB,GAAG;AACzB,eAAKgB,KAAL;AAEA,eAAKlD,QAAL,GAAgB,KAAKmD,WAAL,CAAiBvD,YAAY,CAACwD,QAA9B,EAAwC,IAAxC,CAAhB;AACA,eAAKlC,gBAAL,GAAwB,KAAKiC,WAAL,CAAiBvD,YAAY,CAACyD,gBAA9B,EAAgD,CAAhD,CAAxB;AACA,eAAK/C,YAAL,GAAoB,KAAK6C,WAAL,CAAiBvD,YAAY,CAAC0D,YAA9B,EAA4C,KAAKP,QAAjD,CAApB;AACA,eAAK9C,cAAL,GAAsB,KAAKkD,WAAL,CAAiBvD,YAAY,CAAC2D,cAA9B,EAA8C,IAA9C,CAAtB;AACA,eAAKrD,SAAL,GAAiB,KAAKiD,WAAL,CAAiBvD,YAAY,CAAC4D,SAA9B,EAAyC,IAAzC,CAAjB;AACA,eAAKrD,MAAL,GAAc,KAAKgD,WAAL,CAAiBvD,YAAY,CAAC6D,MAA9B,EAAsC,IAAtC,CAAd;AAEA,eAAKrD,YAAL,GAAoB,KAAK+C,WAAL,CAAiBvD,YAAY,CAAC8D,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKrD,eAAL,GAAuB,KAAK8C,WAAL,CAAiBvD,YAAY,CAAC+D,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAKpD,YAAL,GAAoB,KAAK4C,WAAL,CAAiBvD,YAAY,CAACgE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpD,YAAL,GAAoB,KAAK2C,WAAL,CAAiBvD,YAAY,CAACiE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpD,SAAL,GAAiB,KAAK0C,WAAL,CAAiBvD,YAAY,CAACkE,SAA9B,EAAyC,CAAzC,CAAjB;AACA,eAAKpD,YAAL,GAAoB,KAAKyC,WAAL,CAAiBvD,YAAY,CAACmE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpD,YAAL,GAAoB,KAAKwC,WAAL,CAAiBvD,YAAY,CAACoE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKpD,eAAL,GAAuB,KAAKuC,WAAL,CAAiBvD,YAAY,CAACqE,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAKpD,cAAL,GAAsB,KAAKsC,WAAL,CAAiBvD,YAAY,CAACsE,cAA9B,EAA8C,CAA9C,CAAtB;AACA,eAAKpD,KAAL,GAAa,KAAKqC,WAAL,CAAiBvD,YAAY,CAACuE,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAKpD,KAAL,GAAa,KAAKoC,WAAL,CAAiBvD,YAAY,CAACwE,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAKpD,GAAL,GAAW,KAAKmC,WAAL,CAAiBvD,YAAY,CAACyE,GAA9B,EAAmC,CAAnC,CAAX;AACA,eAAKpD,MAAL,GAAc,KAAKkC,WAAL,CAAiBvD,YAAY,CAAC0E,MAA9B,EAAsC,CAAtC,CAAd,CAtByB,CAwBzB;;AACA,eAAKnD,UAAL,GAAkB;AAAA;AAAA,iDAAlB,CAzByB,CA2BzB;;AACA,eAAKb,YAAL,CAAkBiE,EAAlB,CAAsB1B,KAAD,IAAW;AAC5B;AACA,iBAAKnB,aAAL,GAAqB,IAArB;AACH,WAHD;AAIA,eAAK1B,QAAL,CAAcuE,EAAd,CAAkB1B,KAAD,IAAW;AACxB,gBAAIA,KAAJ,EAAW,CAEV,CAFD,MAEO,CAEN;AACJ,WAND;AAOH;;AAESV,QAAAA,iBAAiB,GAAG;AAC1B,cAAI,CAAC,KAAKW,WAAN,IAAqB,KAAKA,WAAL,CAAiB0B,cAAjB,CAAgCC,MAAhC,IAA0C,CAAnE,EAAsE;AAEtE,eAAKrD,WAAL,GAAmB,EAAnB;AACA,cAAIsD,GAAG,GAAG;AAAA;AAAA,uDAAV;AACAA,UAAAA,GAAG,CAACC,OAAJ,GAAc,IAAd;;AACA,eAAK,IAAMC,UAAX,IAAyB,KAAK9B,WAAL,CAAiB0B,cAA1C,EAA0D;AACtD;AAAA;AAAA,8CAAaK,uBAAb,CAAqCH,GAArC,EAA0CE,UAA1C;AACH;AACJ,SA5JiE,CA8JlE;;;AACUjC,QAAAA,eAAe,GAAG;AACxB,cAAI,CAAC,KAAKG,WAAV,EAAuB;AAEvB,eAAK5B,gBAAL,CAAsB2B,KAAtB,GAA8B,CAA9B;AACA,eAAK7C,QAAL,CAAc6C,KAAd,GAAsB,IAAtB;AACA,eAAKvC,YAAL,CAAkBuC,KAAlB,GAA0B,KAAKE,QAA/B;AACA,eAAK9C,cAAL,CAAoB4C,KAApB,GAA4B,KAAKC,WAAL,CAAiB7C,cAA7C;AACA,eAAKC,SAAL,CAAe2C,KAAf,GAAuB,KAAKC,WAAL,CAAiB5C,SAAxC;AACA,eAAKC,MAAL,CAAY0C,KAAZ,GAAoB,KAAKC,WAAL,CAAiB3C,MAArC;AAEA,eAAKC,YAAL,CAAkByC,KAAlB,GAA0B,KAAKC,WAAL,CAAiB1C,YAAjB,CAA8B4C,IAA9B,EAA1B;AACA,eAAK3C,eAAL,CAAqBwC,KAArB,GAA6B,KAAKC,WAAL,CAAiBzC,eAAjB,CAAiC2C,IAAjC,EAA7B;AACA,eAAKzC,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBvC,YAAjB,CAA8ByC,IAA9B,EAA1B;AACA,eAAKxC,YAAL,CAAkBqC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBtC,YAAjB,CAA8BwC,IAA9B,EAA1B;AACA,eAAKvC,SAAL,CAAeoC,KAAf,GAAuB,KAAKC,WAAL,CAAiBrC,SAAjB,CAA2BuC,IAA3B,EAAvB;AACA,eAAKtC,YAAL,CAAkBmC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBpC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA,eAAKrC,YAAL,CAAkBkC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBnC,YAAjB,CAA8BqC,IAA9B,EAA1B;AACA,eAAKpC,eAAL,CAAqBiC,KAArB,GAA6B,KAAKC,WAAL,CAAiBlC,eAAjB,CAAiCoC,IAAjC,EAA7B;AACA,eAAKnC,cAAL,CAAoBgC,KAApB,GAA4B,KAAKC,WAAL,CAAiBjC,cAAjB,CAAgCmC,IAAhC,EAA5B;AACA,eAAKlC,KAAL,CAAW+B,KAAX,GAAmB,KAAKC,WAAL,CAAiBhC,KAAjB,CAAuBkC,IAAvB,EAAnB;AACA,eAAKjC,KAAL,CAAW8B,KAAX,GAAmB,KAAKC,WAAL,CAAiB/B,KAAjB,CAAuBiC,IAAvB,EAAnB;AACA,eAAKhC,GAAL,CAAS6B,KAAT,GAAiB,KAAKC,WAAL,CAAiB9B,GAAjB,CAAqBgC,IAArB,EAAjB;AACA,eAAK/B,MAAL,CAAY4B,KAAZ,GAAoB,KAAKC,WAAL,CAAiB7B,MAAjB,CAAwB+B,IAAxB,EAApB;AAEA,eAAK7B,UAAL,CAAgB2D,aAAhB,CAA8B,KAAKC,UAAnC;AAEA,eAAK9B,SAAL,CAAe,IAAf;AACH;AAED;AACJ;AACA;;;AACIL,QAAAA,YAAY,CAACb,MAAD,EAAyB;AACjC,eAAKV,OAAL,GAAeU,MAAf;AACA,eAAKR,kBAAL,GAA0B,CAA1B;AACA,eAAKE,aAAL,GAAqB,CAArB,CAHiC,CAIjC;;AACA,eAAKI,mBAAL,GAA2B,EAA3B;;AACA,kBAAQE,MAAR;AACI,iBAAKpC,cAAc,CAACqF,OAApB;AACA,iBAAKrF,cAAc,CAACsF,QAApB;AACI,kBAAI,KAAK7D,WAAL,CAAiBqD,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,qBAAKrD,WAAL,CAAiB8D,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACC,KAAN,EAAlC;AACH,eAHL,CAII;;;AACA,mBAAK7E,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBvC,YAAjB,CAA8ByC,IAA9B,EAA1B;AACA;;AACJ,iBAAKrD,cAAc,CAAC0F,cAApB;AACI;AACA,mBAAK3E,YAAL,CAAkBmC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBpC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA;;AACJ;AACI,kBAAI,KAAK5B,WAAL,CAAiBqD,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,qBAAKrD,WAAL,CAAiB8D,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACG,IAAN,EAAlC;AACH;;AACD;AAjBR;AAmBH;;AAESC,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAK/E,YAAL,CAAkBqC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBtC,YAAjB,CAA8BwC,IAA9B,EAA1B,CAFyB,CAIzB;;AACA,eAAKvB,aAAL,GAAqB,KAAKF,kBAAL,GAA0B,KAAKf,YAAL,CAAkBqC,KAAjE;AACH;;AAES2C,QAAAA,aAAa,GAAG;AACtB,eAAKhE,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAESiE,QAAAA,YAAY,GAAG;AACrB,eAAKjE,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAKK,mBAAL,GAA2B,EAA3B;AACA,eAAK6D,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,eAAKjF,YAAL,CAAkBkC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBnC,YAAjB,CAA8BqC,IAA9B,EAA1B;;AAEA,cAAI,KAAKpC,eAAL,CAAqBiC,KAArB,GAA6B,CAAjC,EAAoC;AAChC;AACA,iBAAK,IAAIgD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlF,YAAL,CAAkBkC,KAAtC,EAA6CgD,CAAC,EAA9C,EAAkD;AAC9C,mBAAKjF,eAAL,CAAqBiC,KAArB,GAA6B,KAAKC,WAAL,CAAiBlC,eAAjB,CAAiCoC,IAAjC,EAA7B;AACA,kBAAM8C,UAAU,GAAG,KAAKvE,kBAAL,GAA2B,KAAKX,eAAL,CAAqBiC,KAArB,GAA6BgD,CAA3E;;AACA,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhF,KAAL,CAAW8B,KAA/B,EAAsCkD,CAAC,EAAvC,EAA2C;AACvC,qBAAKlE,mBAAL,CAAyBmE,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEF,CADmB;AAE1BG,kBAAAA,YAAY,EAAEL,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;AACJ;AACJ,WAbD,MAcK;AACD;AACA,iBAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKhF,KAAL,CAAW8B,KAA/B,EAAsCkD,EAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIF,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKlF,YAAL,CAAkBkC,KAAtC,EAA6CgD,EAAC,EAA9C,EAAkD;AAC9C,qBAAKM,UAAL,CAAgBJ,EAAhB,EAAmBF,EAAnB;AACH;AACJ;AACJ;AACJ;;AAESO,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAKvE,mBAAL,CAAyB4C,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,gBAAM4B,UAAU,GAAG,KAAKxE,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKN,kBAAL,IAA2B8E,UAAU,CAACP,UAA1C,EAAsD;AAClD;AACA,mBAAKjE,mBAAL,CAAyByE,KAAzB;;AACA,mBAAKH,UAAL,CAAgBE,UAAU,CAACJ,KAA3B,EAAkCI,UAAU,CAACH,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESK,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKZ,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESO,QAAAA,UAAU,CAACF,KAAD,EAAeC,YAAf,EAAqC;AACrD,cAAMM,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,cAAMS,QAAQ,GAAG,KAAKC,gBAAL,CAAsBV,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKU,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACR,KAAD,EAA0C;AACvD;AACA,cAAMY,WAAW,GAAG,KAAK9F,KAAL,CAAW8B,KAAX,GAAmB,CAAnB,GAAwB,KAAK7B,GAAL,CAAS6B,KAAT,IAAkB,KAAK9B,KAAL,CAAW8B,KAAX,GAAmB,CAArC,CAAD,GAA4CoD,KAA5C,GAAoD,KAAKjF,GAAL,CAAS6B,KAAT,GAAiB,CAA5F,GAAgG,CAApH;AACA,cAAMiE,MAAM,GAAGrH,gBAAgB,CAAC,KAAKqB,KAAL,CAAW+B,KAAX,GAAmBgE,WAApB,CAA/B;AAEA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACV,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA;AACA,cAAMkB,cAAc,GAAG,CAAClB,YAAD,EAAuBvF,YAAvB,EAA6CE,cAA7C,KAAwE;AAC3F,gBAAIF,YAAY,IAAI,CAApB,EAAuB,OAAO,CAAP;AACvB,gBAAM0G,QAAQ,GAAGxG,cAAc,IAAIF,YAAY,GAAG,CAAnB,CAA/B,CAF2F,CAG3F;;AAEA,gBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,kBAAIuF,YAAY,KAAK,CAArB,EAAwB,OAAO,CAAP;;AACxB,kBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,oBAAMoB,eAAe,GAAIN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAzB;AACA,uBAAO,CAACoB,eAAD,GAAmBD,QAA1B;AACH,eAJD,MAKK;AACD;AACA,oBAAMC,gBAAe,GAAIN,IAAI,CAACQ,IAAL,CAAUtB,YAAY,GAAG,CAAzB,CAAzB;;AACA,uBAAOoB,gBAAe,GAAGD,QAAzB;AACH;AACJ,aAbD,MAaO;AACH;AACA,kBAAInB,YAAY,KAAK,CAArB,EAAwB,OAAO,CAACmB,QAAD,GAAY,CAAnB;;AACxB,kBAAInB,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,oBAAMoB,iBAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;;AACA,uBAAO,CAACmB,QAAD,GAAY,CAAZ,GAAgBC,iBAAe,GAAGD,QAAzC;AACH,eAJD,MAKK;AACD;AACA,oBAAMC,iBAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;;AACA,uBAAOmB,QAAQ,GAAG,CAAX,GAAeC,iBAAe,GAAGD,QAAxC;AACH;AACJ;AACJ,WAhCD;;AAkCA,eAAKxG,cAAL,CAAoBgC,KAApB,GAA4B,KAAKC,WAAL,CAAiBjC,cAAjB,CAAgCmC,IAAhC,EAA5B;AACA,cAAMnC,cAAc,GAAGuG,cAAc,CAAClB,YAAD,EAAe,KAAKvF,YAAL,CAAkBkC,KAAjC,EAAwC,KAAKhC,cAAL,CAAoBgC,KAA5D,CAArC;;AAEA,cAAI,KAAK5B,MAAL,CAAY4B,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AAAEkE,cAAAA,CAAC,EAAElG,cAAL;AAAqBqG,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,cAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,iBAAO;AACHc,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAK9F,MAAL,CAAY4B,KAA1B,GAAkChC,cADlC;AAEHqG,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKjG,MAAL,CAAY4B;AAF1B,WAAP;AAIH;;AAED+D,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAAgF;AACxF,cAAI,CAAC,KAAKhF,aAAV,EAAyB;AACrB,gBAAI/C,MAAJ,EAAY;AACR,mBAAK8I,oBAAL,CAA0BjB,SAA1B,EAAqCE,QAArC;AACH,aAFD,MAGK;AACDgB,cAAAA,OAAO,CAACC,IAAR,CAAa,oCAAb;AACH;;AACD;AACH;;AAED,cAAMC,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,cAAI,CAACD,MAAL,EAAa,OAZ2E,CAcxF;;AACA,cAAME,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAJ,UAAAA,MAAM,CAACG,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAACf,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIe,UAAU,CAACZ,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIY,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,4CAAaC,cAAb,CAA4B,IAA5B,EAAkCP,MAAlC;AACAA,UAAAA,MAAM,CAACQ,IAAP,CAAYC,UAAZ,CAAuBxF,KAAvB,GAA+BnD,gBAAgB,CAACsH,IAAI,CAACsB,KAAL,CAAW9B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,UAAAA,MAAM,CAACQ,IAAP,CAAYG,KAAZ,CAAkB1F,KAAlB,IAA2B,KAAKpC,SAAL,CAAeoC,KAA1C,CAxBwF,CAyBxF;AACA;;AACA+E,UAAAA,MAAM,CAACY,gBAAP;AACH;;AAEef,QAAAA,oBAAoB,CAACjB,SAAD,EAAsCE,QAAtC,EAA0E;AAAA;;AAAA;AAC1G;AACA,gBAAM+B,UAAU,GAAG,sDAAnB,CAF0G,CAG1G;;AACAC,YAAAA,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDH,UAAjD,EACKI,IADL,CACWC,IAAD,IAAkB;AACpBpK,cAAAA,YAAY,CAACqK,OAAb,CAAqB;AAACD,gBAAAA,IAAI,EAAEA;AAAP,eAArB,EAAmC,CAACE,GAAD,EAAMC,MAAN,KAAiB;AAChD,oBAAID,GAAJ,EAAS;AACLtB,kBAAAA,OAAO,CAACwB,KAAR,CAAcF,GAAd;AACA;AACH;;AACD,gBAAA,KAAI,CAACtH,aAAL,GAAqBuH,MAArB;;AACA,oBAAMrB,MAAM,GAAG,KAAI,CAACC,iBAAL,EAAf;;AACA,oBAAI,CAACD,MAAL,EAAa,OAPmC,CAShD;;AACA,oBAAME,UAAU,GAAG,KAAI,CAACC,IAAL,CAAUC,gBAAV,EAAnB;;AACAJ,gBAAAA,MAAM,CAACG,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAACf,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIe,UAAU,CAACZ,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIY,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,kDAAaC,cAAb,CAA4B,KAA5B,EAAkCP,MAAlC;AACAA,gBAAAA,MAAM,CAACQ,IAAP,CAAYC,UAAZ,CAAuBxF,KAAvB,GAA+BnD,gBAAgB,CAACsH,IAAI,CAACsB,KAAL,CAAW9B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,gBAAAA,MAAM,CAACQ,IAAP,CAAYG,KAAZ,CAAkB1F,KAAlB,IAA2B,KAAI,CAACpC,SAAL,CAAeoC,KAA1C;AACA+E,gBAAAA,MAAM,CAACY,gBAAP;AACH,eArBD;AAsBH,aAxBL;AAJ0G;AA6B7G;;AAESX,QAAAA,iBAAiB,GAA4B;AACnD,cAAMsB,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAaC,YAAhC,EAA8C,KAAK3H,aAAnD,CAAnB;;AACA,cAAI,CAACyH,UAAL,EAAiB;AACbzB,YAAAA,OAAO,CAACwB,KAAR,CAAc,8CAAd;AACA,mBAAO,IAAP;AACH,WALkD,CAOnD;;;AACA,cAAMtB,MAAM,GAAGuB,UAAU,CAACG,YAAX;AAAA;AAAA,mDAAf;;AACA,cAAI,CAAC1B,MAAL,EAAa;AACTF,YAAAA,OAAO,CAACwB,KAAR,CAAc,uDAAd;AACAC,YAAAA,UAAU,CAACI,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAI5K,MAAJ,EAAY;AACRwK,YAAAA,UAAU,CAACK,IAAX,GAAkB3J,OAAO,CAAC4J,mBAA1B;AACH;;AAED,iBAAO7B,MAAP;AACH;;AAED8B,QAAAA,UAAU,CAACT,MAAD,EAAiBvC,QAAjB,EAAiCiD,QAAjC,EAAiDC,QAAjD,EAAmE;AACzE,cAAI,CAACX,MAAL,EAAa;AAEb,cAAMY,UAAU,GAAG;AAAA;AAAA,wCAAWT,OAAX,CAAmB,KAAKrB,IAAxB,EAA8BkB,MAA9B,CAAnB;AACA,cAAI,CAACY,UAAL,EAAiB;AAEjBA,UAAAA,UAAU,CAAC5B,gBAAX,CAA4BvB,QAA5B;AACAmD,UAAAA,UAAU,CAACC,gBAAX,CAA4BH,QAA5B,EAPyE,CAQzE;AACA;;AACA,eAAKI,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,0CAAWC,UAAX,CAAsBH,UAAtB;AACH,WAFD,EAEGD,QAFH;AAGH;AAED;AACJ;AACA;;;AACcK,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKnK,QAAN,IAAkB,CAAC,KAAKA,QAAL,CAAc6C,KAArC,EAA4C;AACxC;AACH;;AAED,kBAAQ,KAAKxB,OAAb;AAEI,iBAAK1B,cAAc,CAAC2B,IAApB;AACI,mBAAK8I,gBAAL;AACA;;AACJ,iBAAKzK,cAAc,CAACqF,OAApB;AACI,mBAAKqF,mBAAL;AACA;;AACJ,iBAAK1K,cAAc,CAACsF,QAApB;AACI,mBAAKqF,oBAAL;AACA;;AACJ,iBAAK3K,cAAc,CAAC0F,cAApB;AACI,mBAAKkF,0BAAL;AACA;;AACJ,iBAAK5K,cAAc,CAAC6K,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;;AAqBA,eAAKlJ,kBAAL,IAA2B4I,SAA3B;AACA,eAAKjJ,gBAAL,CAAsB2B,KAAtB,IAA+BsH,SAA/B;AAEA,eAAKlH,SAAL;AACH;;AAESmH,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAK7I,kBAAL,IAA2B,KAAKnB,YAAL,CAAkByC,KAAjD,EAAwD;AACpD,iBAAKD,YAAL,CAAkBjD,cAAc,CAACqF,OAAjC;AACH;AACJ;;AAESqF,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKnK,SAAL,CAAe2C,KAApB,EACI,KAAKD,YAAL,CAAkBjD,cAAc,CAACsF,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAK1D,kBAAL,IAA2B,KAAKlB,eAAL,CAAqBwC,KAApD,EAA2D;AACvD,mBAAKD,YAAL,CAAkBjD,cAAc,CAACsF,QAAjC;AACH;AACJ;AACJ;;AAESqF,QAAAA,oBAAoB,GAAG;AAC7B,cAAI,KAAK/I,kBAAL,GAA0B,KAAKhB,YAAL,CAAkBsC,KAAhD,EAAuD;AACnD,iBAAK4C,YAAL;AACA,gBAAI,KAAKtF,MAAT,EACI,KAAKyC,YAAL,CAAkBjD,cAAc,CAAC0F,cAAjC,EADJ,KAGI,KAAKzC,YAAL,CAAkBjD,cAAc,CAAC6K,SAAjC;AACJ;AACH,WAR4B,CAU7B;;;AACA,cAAI,CAAC,KAAKhJ,WAAV,EAAuB;AACnB,iBAAKgE,aAAL;AACH,WAFD,MAGK,IAAI,KAAKjE,kBAAL,IAA2B,KAAKE,aAApC,EAAmD;AACpD,iBAAK8E,OAAL;;AACA,gBAAI,KAAK3F,eAAL,CAAqBiC,KAArB,IAA8B,CAAlC,EAAqC;AACjC,mBAAK0C,gBAAL;AACH,aAFD,MAGK;AACD;AACA,mBAAK9D,aAAL,GAAqB,KAAKF,kBAAL,GAA0B,QAA/C;AACH;AACJ;;AAED,cAAImJ,WAAW,GAAG,KAAK7I,mBAAL,CAAyB4C,MAAzB,GAAkC,CAApD,CAzB6B,CA0B7B;;AACA,eAAK2B,mBAAL;;AACA,cAAIsE,WAAW,IAAI,KAAK7I,mBAAL,CAAyB4C,MAAzB,IAAmC,CAAtD,EAAyD;AACrD,iBAAKc,gBAAL;AACH;AACJ;;AAESgF,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKhJ,kBAAL,IAA2B,KAAKb,YAAL,CAAkBmC,KAAjD,EAAwD;AACpD,iBAAKD,YAAL,CAAkBjD,cAAc,CAACqF,OAAjC;AACH;AACJ;;AAESyF,QAAAA,qBAAqB,GAAG;AAC9B;AACA,eAAKzK,QAAL,CAAc6C,KAAd,GAAsB,KAAtB;AACA,eAAK7C,QAAL,CAAc2K,MAAd;AACH;;AAnkBiE,O,UAE3DlB,mB,GAA6B,U;;;;;iBAGV,C;;;;;;;iBAGU;AAAA;AAAA,2C;;;;;;;iBAGF;AAAA;AAAA,yC", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Component, Prefab, Color, Vec3, Quat, assetManager } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletProperty, BulletController } from './BulletController';\r\nimport { EmitterData } from '../data/bullet/EmitterData';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from \"./EventGroup\";\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\n\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu  } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n// 用枚举定义属性\r\nexport enum eEmitterProp {\r\n    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop, \r\n    InitialDelay, PreWarmDuration, EmitBulletID, EmitDuration, EmitInterval, EmitPower, LoopInterval,\r\n    PerEmitCount, PerEmitInterval, PerEmitOffsetX, \r\n    Angle, Count, Arc, Radius,\r\n    TotalElapsedTime, \r\n}\r\n\r\n@ccclass('Emitter')\r\n// @inspector('editor/inspector/components/emitter')\r\n@menu('子弹系统/发射器')\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class Emitter extends PropertyContainerComponent<eEmitterProp> {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({displayName: \"子弹ID\"})\r\n    public bulletID: number = 0;\r\n\r\n    @property({type: EmitterData, displayName: \"发射器属性\"})\r\n    readonly emitterData: EmitterData = new EmitterData();\r\n\r\n    @property({type: BulletData, displayName: \"子弹属性\"})\r\n    readonly bulletData: BulletData = new BulletData();\r\n\r\n    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)\r\n    public isActive!: Property<boolean>;\r\n    public isOnlyInScreen!: Property<boolean>;\r\n    public isPreWarm!: Property<boolean>;\r\n    public isLoop!: Property<boolean>;\r\n    public initialDelay!: Property<number>;\r\n    public preWarmDuration!: Property<number>;\r\n    public emitBulletID!: Property<number>;\r\n    public emitDuration!: Property<number>;\r\n    public emitInterval!: Property<number>;\r\n    public emitPower!: Property<number>;\r\n    public loopInterval!: Property<number>;\r\n    public perEmitCount!: Property<number>;\r\n    public perEmitInterval!: Property<number>;\r\n    public perEmitOffsetX!: Property<number>;\r\n    public angle!: Property<number>;\r\n    public count!: Property<number>;\r\n    public arc!: Property<number>;\r\n    public radius!: Property<number>;\r\n    public totalElapsedTime!: Property<number>;\r\n    // 以下用于事件组修改子弹的属性，（不直接修改bulletData)\r\n    public bulletProp!: BulletProperty;\r\n\r\n    // 发射器自己的事件组\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    // 私有变量\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n    protected _bulletPrefab: Prefab|null = null;\r\n    protected _prewarmEffectPrefab: Prefab|null = null;\r\n    protected _emitEffectPrefab: Prefab|null = null;\r\n\r\n    // Per-emit timing tracking\r\n    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];\r\n\r\n    get isEmitting(): boolean { return this._isEmitting; }\r\n    get status(): eEmitterStatus { return this._status; }\r\n    get statusElapsedTime(): number { return this._statusElapsedTime; }\r\n\r\n    protected onLoad() : void {\r\n        this.createProperties();\r\n        this.createEventGroups();\r\n    }\r\n\r\n    protected onEnable(): void {\r\n        BulletSystem.onCreateEmitter(this);\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        BulletSystem.onDestroyEmitter(this);\r\n    }\r\n\r\n    public onLostFocusInEditor(): void {\r\n        this.updatePropertiesInEditor();\r\n        this.createEventGroups();\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.resetProperties();\r\n        this.changeStatus(eEmitterStatus.None);\r\n        this._isEmitting = false;\r\n        this.totalElapsedTime.value = 0;\r\n    }\r\n\r\n    public updatePropertiesInEditor() {\r\n        if (!this.emitterData) return;\r\n        \r\n        this.isActive.value = true;\r\n        this.emitBulletID.value = this.bulletID;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval();\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        this.angle.value = this.emitterData.angle.eval();\r\n        this.count.value = this.emitterData.count.eval();\r\n        this.arc.value = this.emitterData.arc.eval();\r\n        this.radius.value = this.emitterData.radius.eval();\r\n\r\n        this.notifyAll(true);\r\n    }\r\n\r\n    protected createProperties() {\r\n        this.clear();\r\n        \r\n        this.isActive = this.addProperty(eEmitterProp.IsActive, true);\r\n        this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);\r\n        this.emitBulletID = this.addProperty(eEmitterProp.EmitBulletID, this.bulletID);\r\n        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);\r\n        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);\r\n        this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);\r\n\r\n        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);\r\n        this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, 0);\r\n        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);\r\n        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);\r\n        this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);\r\n        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);\r\n        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);\r\n        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);\r\n        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);\r\n        this.angle = this.addProperty(eEmitterProp.Angle, 0);\r\n        this.count = this.addProperty(eEmitterProp.Count, 1);\r\n        this.arc = this.addProperty(eEmitterProp.Arc, 0);\r\n        this.radius = this.addProperty(eEmitterProp.Radius, 0);\r\n\r\n        // 子弹相关属性\r\n        this.bulletProp = new BulletProperty();\r\n\r\n        // 子弹表->Prefab路径\r\n        this.emitBulletID.on((value) => {\r\n            // TODO: reload bullet prefab\r\n            this._bulletPrefab = null;\r\n        });\r\n        this.isActive.on((value) => {\r\n            if (value) {\r\n                \r\n            } else {\r\n                \r\n            }\r\n        });\r\n    }\r\n\r\n    protected createEventGroups() {\r\n        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;\r\n\r\n        this.eventGroups = [];\r\n        let ctx = new EventGroupContext();\r\n        ctx.emitter = this;\r\n        for (const eventGroup of this.emitterData.eventGroupData) {\r\n            BulletSystem.createEmitterEventGroup(ctx, eventGroup);\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        if (!this.emitterData) return;\r\n        \r\n        this.totalElapsedTime.value = 0;\r\n        this.isActive.value = true;\r\n        this.emitBulletID.value = this.bulletID;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval();\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        this.angle.value = this.emitterData.angle.eval();\r\n        this.count.value = this.emitterData.count.eval();\r\n        this.arc.value = this.emitterData.arc.eval();\r\n        this.radius.value = this.emitterData.radius.eval();\r\n\r\n        this.bulletProp.resetFromData(this.bulletData);\r\n\r\n        this.notifyAll(true);\r\n    }\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        this._nextEmitTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n        switch (status) {\r\n            case eEmitterStatus.Prewarm:\r\n            case eEmitterStatus.Emitting:\r\n                if (this.eventGroups.length > 0) {\r\n                    this.eventGroups.forEach(group => group.start());\r\n                }\r\n                // re-eval\r\n                this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                // re-eval\r\n                this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n                break;\r\n            default:\r\n                if (this.eventGroups.length > 0) {\r\n                    this.eventGroups.forEach(group => group.stop());\r\n                }\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // re-eval\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        \r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        // re-eval\r\n        // this.count.value = this.emitterData.count.eval();\r\n        // this.arc.value = this.emitterData.arc.eval();\r\n        // this.radius.value = this.emitterData.radius.eval();\r\n        // this.angle.value = this.emitterData.angle.eval();\r\n        // this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n\r\n        if (this.perEmitInterval.value > 0) {\r\n            // Generate bullets in time-sorted order directly\r\n            for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n                const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);\r\n                for (let i = 0; i < this.count.value; i++) {\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;\r\n        const radian = degreesToRadians(this.angle.value + angleOffset);\r\n        \r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * odd number to the right, even number to the left\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex, with the rules:\r\n        // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;\r\n        const getEmitOffsetX = (perEmitIndex: number, perEmitCount: number, perEmitOffsetX: number) => {\r\n            if (perEmitCount <= 1) return 0;\r\n            const interval = perEmitOffsetX / (perEmitCount - 1);\r\n            //const middle = 0;\r\n\r\n            if (perEmitCount % 2 === 1) {\r\n                // 奇数情况\r\n                if (perEmitIndex === 0) return 0;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle =  Math.floor(perEmitIndex / 2);\r\n                    return -stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle =  Math.ceil(perEmitIndex / 2);\r\n                    return stepsFromMiddle * interval;\r\n                }\r\n            } else {\r\n                // 偶数情况\r\n                if (perEmitIndex === 0) return -interval / 2;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -interval / 2 - stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return interval / 2 + stepsFromMiddle * interval;\r\n                }\r\n            }\r\n        }\r\n\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);\r\n\r\n        if (this.radius.value <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n        \r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.radius.value + perEmitOffsetX,\r\n            y: direction.y * this.radius.value\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {\r\n        if (!this._bulletPrefab) {\r\n            if (EDITOR) {\r\n                this.createBulletInEditor(direction, position);\r\n            }\r\n            else {\r\n                console.warn(\"Emitter: No bullet prefab assigned\");\r\n            }\r\n            return;\r\n        }\r\n        \r\n        const bullet = this.instantiateBullet();\r\n        if (!bullet) return;\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bullet.node.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        BulletSystem.onCreateBullet(this, bullet);\r\n        bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.prop.speed.value *= this.emitPower.value;\r\n        // 为什么需要在这里resetEventGroups?\r\n        // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性\r\n        bullet.resetEventGroups();\r\n    }\r\n\r\n    protected async createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        // use a default bullet prefab\r\n        const prefabPath = 'db://assets/resources/Game/prefabs/Bullet_New.prefab';\r\n        // @ts-ignore\r\n        Editor.Message.request('asset-db', 'query-uuid', prefabPath)\r\n            .then((uuid: string) => {\r\n                assetManager.loadAny({uuid: uuid}, (err, prefab) => {\r\n                    if (err) {\r\n                        console.error(err);\r\n                        return;\r\n                    }\r\n                    this._bulletPrefab = prefab;\r\n                    const bullet = this.instantiateBullet();\r\n                    if (!bullet) return;\r\n\r\n                    // Set bullet position relative to emitter\r\n                    const emitterPos = this.node.getWorldPosition();\r\n                    bullet.node.setWorldPosition(\r\n                        emitterPos.x + position.x,\r\n                        emitterPos.y + position.y,\r\n                        emitterPos.z\r\n                    );\r\n\r\n                    BulletSystem.onCreateBullet(this, bullet);\r\n                    bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n                    bullet.prop.speed.value *= this.emitPower.value;\r\n                    bullet.resetEventGroups();\r\n                });\r\n            });\r\n    }\r\n\r\n    protected instantiateBullet(): BulletController | null {\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab!);\r\n        if (!bulletNode) {\r\n            console.error(\"Emitter: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(BulletController);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {\r\n        if (!prefab) return;\r\n\r\n        const effectNode = ObjectPool.getNode(this.node, prefab);\r\n        if (!effectNode) return;\r\n\r\n        effectNode.setWorldPosition(position);\r\n        effectNode.setWorldRotation(rotation);\r\n        // Play the effect and destroy it after duration\r\n        // effectNode.getComponent(ParticleSystem)?.play();\r\n        this.scheduleOnce(() => {\r\n            ObjectPool.returnNode(effectNode);\r\n        }, duration);\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.isActive || !this.isActive.value) {\r\n            return;\r\n        }\r\n\r\n        switch (this._status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        this._statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime.value += deltaTime;\r\n\r\n        this.notifyAll();\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm.value)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration.value) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        if (this._statusElapsedTime > this.emitDuration.value) {\r\n            this.stopEmitting();\r\n            if (this.isLoop)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n        \r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._statusElapsedTime >= this._nextEmitTime) {\r\n            this.tryEmit();\r\n            if (this.perEmitInterval.value <= 0) {\r\n                this.scheduleNextEmit();\r\n            }\r\n            else {\r\n                // 开始这一波\r\n                this._nextEmitTime = this._statusElapsedTime + 10000000;\r\n            }\r\n        }\r\n        \r\n        let wasEmitting = this._perEmitBulletQueue.length > 0;\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n        if (wasEmitting && this._perEmitBulletQueue.length <= 0) {\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n        this.isActive.value = false;\r\n        this.isActive.notify();\r\n    }\r\n}\r\n"]}