import { _decorator, misc, Component, Enum, Vec3, Node } from 'cc';
const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;
import { IMovable } from './IMovable';
import { BulletSystem } from '../bullet/BulletSystem';

export enum eSpriteDefaultFacing {
    Right = 0,    // →
    Up = -90,     // ↑
    Down = 90,    // ↓
    Left = 180    // ←
}

@ccclass('Movable')
@executeInEditMode
export class Movable extends Component implements IMovable {

    @property({type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向'})
    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;

    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向
    public isTrackingTarget: boolean = false;     // 是否正在追踪目标
    public speed: number = 1;                     // 速度
    public speedAngle: number = 0;                // 速度方向 (用角度表示)
    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）
    public acceleration: number = 0;              // 加速度
    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)

    public target: Node | null = null;            // 追踪的目标节点
    public arrivalDistance: number = 10;          // 到达目标的距离

    private _position: Vec3 = new Vec3();

    private _isVisible: boolean = true;           // 是否可见
    public onBecomeVisible: Function | null = null;
    public onBecomeInvisible: Function | null = null;

    public tick(dtInMiliseconds: number): void {
        // 毫秒 -> 秒
        let dt = dtInMiliseconds / 1000;
        
        // 根据移动属性更新位置
        this.node.getPosition(this._position);
        
        // Convert speed and angle to velocity vector
        let velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
        let velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));

        if (this.isTrackingTarget && this.target) {
            const targetPos = this.target.getPosition();
            const currentPos = this.node.getPosition();
            
            // Calculate direction to target
            const directionX = targetPos.x - currentPos.x;
            const directionY = targetPos.y - currentPos.y;
            const distance = Math.sqrt(directionX * directionX + directionY * directionY);
            
            if (distance > 0) {
                // Calculate desired angle to target
                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));
                
                // Smoothly adjust speedAngle toward target
                const angleDiff = desiredAngle - this.speedAngle;
                // Normalize angle difference to [-180, 180] range
                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;
                
                // Apply tracking adjustment (you can add a trackingStrength property to control this)
                const trackingStrength = 1.0; // Can be made configurable
                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable
                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);
                
                this.speedAngle += turnAmount * trackingStrength;
                
                // Recalculate velocity with new angle
                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));
            }
        }

        // Convert acceleration and angle to acceleration vector
        const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));
        const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle));

        // Update velocity vector: v = v + a * dt
        const newVelocityX = velocityX + accelerationX * dt;
        const newVelocityY = velocityY + accelerationY * dt;

        // Convert back to speed and angle
        this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);
        this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));

        // Update position: p = p + v * dt
        this._position.x += newVelocityX * dt;
        this._position.y += newVelocityY * dt;
        this.node.setPosition(this._position);
        
        if (this.isFacingMoveDir && this.speed > 0) {
            const movementAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));
            const finalAngle = movementAngle + this.defaultFacing;
            this.node.setRotationFromEuler(0, 0, finalAngle);
        }

        this.checkVisibility();
    }

    public checkVisibility(): void {
        // check visibility by comparing with BulletSystem.worldBounds and this._position
        const visibleSize = BulletSystem.worldBounds;
        const isVisible = this._position.x >= visibleSize.xMin && this._position.x <= visibleSize.xMax &&
                          this._position.y >= visibleSize.yMin && this._position.y <= visibleSize.yMax;

        this.setVisible(isVisible);
    }

    public setVisible(visible: boolean) {
        if (this._isVisible === visible) return;

        this._isVisible = visible;
        if (visible && this.onBecomeVisible) {
            this.onBecomeVisible();
        } else if (!visible && this.onBecomeInvisible) {
            this.onBecomeInvisible();
        }
    }

    /**
     * Set the target to track
     */
    public setTarget(target: Node | null): void {
        this.target = target;
        this.isTrackingTarget = target !== null;
    }
}