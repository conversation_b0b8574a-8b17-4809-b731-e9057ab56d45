import { _decorator, misc, Component, Node, Sprite, Color, CCString } from 'cc';
import { EDITOR } from 'cc/env';
import { BulletData } from '../data/bullet/BulletData';
import { ObjectPool } from './ObjectPool';
import { Movable, eSpriteDefaultFacing } from '../move/Movable';
import { BulletSystem } from './BulletSystem';
import { EventGroup, EventGroupContext } from './EventGroup';
import { Property, PropertyContainer } from './PropertyContainer';
import { Emitter } from './Emitter';
const { ccclass, property, executeInEditMode } = _decorator;

export class BulletProperty extends PropertyContainer<number> {
    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)
    public delayDestroy!: Property<number>;            // 延迟销毁时间

    public speed!: Property<number>;                   // 子弹速度
    public speedAngle!: Property<number>;              // 子弹速度角度
    public acceleration!: Property<number>;            // 子弹加速度
    public accelerationAngle!: Property<number>;       // 子弹加速度角度
    public scale!: Property<number>;                   // 子弹缩放
    public color!: Property<Color>;                    // 子弹颜色
    public defaultFacing!: Property<eSpriteDefaultFacing>;          // 子弹初始朝向

    public isDestroyOutScreen!: Property<boolean>;     // 是否超出屏幕销毁
    public isDestructive!: Property<boolean>;          // 是否可被破坏
    public isDestructiveOnHit!: Property<boolean>;     // 命中时是否被销毁
    public isFacingMoveDir!: Property<boolean>;        // 是否面向移动方向
    public isTrackingTarget!: Property<boolean>;       // 是否追踪目标

    constructor() {
        super();
        this.duration = this.addProperty(0, 6000);
        this.delayDestroy = this.addProperty(1, 0);
        this.speed = this.addProperty(2, 600);
        this.speedAngle = this.addProperty(3, 0);
        this.acceleration = this.addProperty(4, 0);
        this.accelerationAngle = this.addProperty(5, 0);
        this.scale = this.addProperty(6, 1);
        this.color = this.addProperty(7, Color.WHITE);
        this.defaultFacing = this.addProperty<eSpriteDefaultFacing>(8, eSpriteDefaultFacing.Up);
        this.isDestroyOutScreen = this.addProperty(9, true);
        this.isDestructive = this.addProperty(10, false);
        this.isDestructiveOnHit = this.addProperty(11, false);
        this.isFacingMoveDir = this.addProperty(12, false);
        this.isTrackingTarget = this.addProperty(13, false);
    }

    public resetFromData(data: BulletData) {
        this.duration.value = data.duration.eval(); 
        this.delayDestroy.value = data.delayDestroy.eval(); 
        this.speed.value = data.speed.eval(); 
        // this.speedAngle.value = data.speedAngle.eval(); 
        this.acceleration.value = data.acceleration.eval(); 
        this.accelerationAngle.value = data.accelerationAngle.eval(); 
        this.scale.value = data.scale.eval(); 
        // this.color.value = data.color.eval(); 
        this.isDestroyOutScreen.value = data.isDestroyOutScreen; 
        this.isDestructive.value = data.isDestructive; 
        this.isDestructiveOnHit.value = data.isDestructiveOnHit; 
        this.isFacingMoveDir.value = data.isFacingMoveDir; 
        this.isTrackingTarget.value = data.isTrackingTarget;
    }

    public copyFrom(other: BulletProperty) {
        this.forEachProperty((k, prop) => {
            prop.value = other.getPropertyValue(k)!;
        });
    }

    public clear() {
        // Clear all listeners
        this.forEachProperty((k, prop) => prop.clear());
    }
}

// 子弹 Bullet
// 如何集成到项目里? 考虑把这个类改为BulletController, 控制移动等属性, 作为一个component加入到Bullet:Entity里去
@ccclass('BulletController')
@executeInEditMode(true)
export class BulletController extends Component {

    @property({type: Movable, displayName: "移动组件"})
    public mover!: Movable;

    // TODO: 这里后续不处理子弹的sprite显示
    @property({type: Sprite, displayName: "子弹精灵"})
    public bulletSprite: Sprite|null = null;
    
    public isRunning: boolean = false;
    public elapsedTime: number = 0;

    public emitter!: Emitter;
    public bulletData!: BulletData;

    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里
    public prop: BulletProperty = new BulletProperty();

    public eventGroups: EventGroup[] = [];

    onLoad(): void {
        if (!this.mover) {
            this.mover = this.getComponent(Movable)?.addComponent(Movable)!;
        }
        this.mover.onBecomeInvisible = () => {
            if (this.prop.isDestroyOutScreen.value) {
                BulletSystem.onDestroyBullet(this);
            }
        };
 
        this.prop.defaultFacing.value = this.mover.defaultFacing;
        // listen to property changes
        this.prop.isFacingMoveDir.on((value) => {
            this.mover.isFacingMoveDir = value;
        });
        this.prop.isTrackingTarget.on((value) => {
            this.mover.isTrackingTarget = value;
        });
        this.prop.speed.on((value) => {
            this.mover.speed = value;
        });
        this.prop.speedAngle.on((value) => {
            this.mover.speedAngle = value;
        });
        this.prop.acceleration.on((value) => {
            this.mover.acceleration = value;
        });
        this.prop.accelerationAngle.on((value) => {
            this.mover.accelerationAngle = value;
        });
        this.prop.scale.on((value) => {
            this.node.setScale(value, value, value);
        });
        this.prop.color.on((value) => {
            if (this.bulletSprite) {
                this.bulletSprite.color = value;
            }
        });
    }

    public onCreate(emitter: Emitter): void {
        this.isRunning = true;
        this.elapsedTime = 0;
        this.emitter = emitter;
        this.bulletData = emitter.bulletData!;

        this.resetProperties();
    }

    public resetProperties(): void {
        if (!this.emitter) return;

        this.prop.copyFrom(this.emitter.bulletProp);
        this.prop.notifyAll(true);
    }

    public resetEventGroups(): void {
        // create event groups here
        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {
            let ctx = new EventGroupContext();
            ctx.bullet = this;
            ctx.emitter = this.emitter;
            for (const eventName of this.bulletData.eventGroupData) {
                BulletSystem.createBulletEventGroup(ctx, eventName);
            }
        }
    }

    public tick(dt:number) : void {
        if (!this.isRunning) return;

        this.elapsedTime += dt;
        if (this.elapsedTime > this.prop.duration.value) {
            this.destroySelf();
            return;
        }

        this.mover?.tick(dt);
        this.prop.notifyAll();
    }

    public destroySelf(): void {
        this.isRunning = false;
        if (this.eventGroups && this.eventGroups.length > 0)
        {
            this.eventGroups.forEach(group => group.stop()); // stop all event groups before destroying the bullet itself.
            this.eventGroups = []; // clear the event groups array
        }
        const cb = () => {
            if (!this.node || !this.node.isValid) return;
            
            if (EDITOR) {
                this.node.destroy();
            } else {
                ObjectPool.returnNode(this.node);
            }
        };
        if (this.prop.delayDestroy && this.prop.delayDestroy.value > 0) {
            this.scheduleOnce(() => {
                cb();
            }, this.prop.delayDestroy.value);
        } else {
            cb();
        }
    }

    /**
     * TODO: 如果后续自己写碰撞, 这里要相应进行替换
     */
    onCollisionEnter(other: Node, self: Node): void {
        // 判断另一个node也是子弹或者非子弹, 进行相应处理
        // 根据this.isDestructive 和 this.isDestructiveOnHit
        BulletSystem.onDestroyBullet(this);
    }
    
}
