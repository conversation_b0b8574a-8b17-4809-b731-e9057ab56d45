import RPNCalculator, { R<PERSON><PERSON><PERSON>ram, RPNContext, SerializableRPNProgram } from "db://assets/scripts/Game/utils/RPN";
import { _decorator, Enum } from "cc";
const { ccclass, property } = _decorator;

@ccclass('ExpressionValue')
export class ExpressionValue {
    @property({visible: false})
    public value: number = 0;
    @property({visible: false})
    public isExpression: boolean = false;
    @property({visible: false})
    public expression: string = '';

    // Serializable compiled program for editor storage
    @property({type: SerializableRPNProgram, visible: false})
    private serializedProgram: SerializableRPNProgram | null = null;

    // Runtime program (not serialized)
    private program: RPNProgram | null = null;

    static calc = new RPNCalculator();
    
    @property({displayName: '值'})
    public get raw(): string {
        return this.isExpression ? this.expression : this.value.toString();
    }
    public set raw(value: string) {
        // 替换全角为半角
        this.expression = value.replace(/[\uff01-\uff5e]/g, (ch) => String.fromCharCode(ch.charCodeAt(0) - 0xfee0));
        // if includes any 'letters' then it is a expression
        this.isExpression = /[a-zA-Z]/.test(value);
        if (!this.isExpression) {
            // try parse number
            const parsed = parseFloat(value);
            if (!isNaN(parsed)) {
                this.value = parsed;
            }
        }
        else {
            // Compile and store both runtime and serializable versions
            this.program = ExpressionValue.calc.compile(this.expression);
            this.serializedProgram = SerializableRPNProgram.fromRPNProgram(this.program);
        }
    }

    public eval(context: RPNContext|null = null): number {
        if (this.isExpression) {
            if (!this.program && this.serializedProgram?.isCompiled()) {
                this.program = this.serializedProgram.toRPNProgram(ExpressionValue.calc.registry);
            }

            if (this.program) {
                const result = this.program.evaluate(context || {});
                return typeof result === 'number' ? result : (result ? 1 : 0); // return 0 if false else 1
            }
            return this.value;
        }
        return this.value;
    }

    // Get debug info about the compiled program
    public getDebugInfo(): any {
        if (!this.isExpression) {
            return { type: 'number', value: this.value };
        }

        return {
            type: 'expression',
            expression: this.expression,
            isCompiled: this.serializedProgram?.isCompiled(),
            variables: this.serializedProgram?.varNames,
            codeLength: this.serializedProgram?.code.length,
            constantsCount: this.serializedProgram?.consts.length
        };
    }

    constructor(str?: string) {
        this.raw = str || '';
    }
}