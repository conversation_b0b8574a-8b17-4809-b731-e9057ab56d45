{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/FightEntity.ts"], "names": ["_decorator", "Entity", "<PERSON>llComp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "res", "ccclass", "FightEntity", "enemy", "isDead", "type", "maxHp", "curHp", "attack", "_skillComp", "_buffComp", "init", "addComp", "skillComp", "buff<PERSON><PERSON>p", "CastSkill", "skillID", "Cast", "to<PERSON><PERSON>", "hurt", "damage", "ApplyBuffEffect", "buff", "effectData", "EffectType", "Kill", "Hurt", "param", "length", "RemoveBuffEffect"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAGFC,MAAAA,M;;AACAC,MAAAA,S;;AACAC,MAAAA,Q;;AACWC,MAAAA,G,iBAAAA,G;;;;;;;;;OALZ;AAAEC,QAAAA;AAAF,O,GAAcL,U;;yBASCM,W,WADpBD,OAAO,CAAC,aAAD,C,gBAAR,MACqBC,WADrB;AAAA;AAAA,4BACgD;AAAA;AAAA;AAAA,eAC5CC,KAD4C,GACpC,IADoC;AAC9B;AAD8B,eAE5CC,MAF4C,GAEnC,KAFmC;AAE5B;AAF4B,eAG5CC,IAH4C,GAGrC,CAHqC;AAGlC;AAHkC,eAK5CC,KAL4C,GAK7B,CAL6B;AAAA,eAM5CC,KAN4C,GAM7B,CAN6B;AAAA,eAO5CC,MAP4C,GAO5B,CAP4B;AAAA,eASpCC,UAToC,GASP,IATO;AAAA,eAUpCC,SAVoC,GAUT,IAVS;AAAA;;AAY5CC,QAAAA,IAAI,GAAG;AACH,eAAKF,UAAL,GAAkB;AAAA;AAAA,uCAAlB;AACA,eAAKG,OAAL,CAAa,OAAb,EAAsB,KAAKH,UAA3B;AACA,eAAKC,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACA,eAAKE,OAAL,CAAa,MAAb,EAAqB,KAAKF,SAA1B;AACA,gBAAMC,IAAN;AACH;;AAEY,YAATE,SAAS,GAAG;AACZ,iBAAO,KAAKJ,UAAZ;AACH;;AAEW,YAARK,QAAQ,GAAG;AACX,iBAAO,KAAKJ,SAAZ;AACH;;AAEDK,QAAAA,SAAS,CAACC,OAAD,EAAkB;AACvB,eAAKH,SAAL,CAAeI,IAAf,CAAoB,IAApB,EAA0BD,OAA1B;AACH;;AAEDE,QAAAA,KAAK,GAAW;AACZ,cAAI,KAAKd,MAAT,EAAiB;AACb,mBAAO,KAAP;AACH;;AACD,eAAKA,MAAL,GAAc,IAAd;AACA,iBAAO,IAAP;AACH;;AAEDe,QAAAA,IAAI,CAACC,MAAD,EAAiB,CACpB;;AAEDC,QAAAA,eAAe,CAACC,IAAD,EAAkBC,UAAlB,EAAmD;AAC9D,kBAAOA,UAAU,CAAClB,IAAlB;AACI,iBAAK;AAAA;AAAA,4BAAImB,UAAJ,CAAeC,IAApB;AACI,mBAAKP,KAAL;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIM,UAAJ,CAAeE,IAApB;AACI,kBAAIH,UAAU,CAACI,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EACA;AACI,qBAAKT,IAAL,CAAUI,UAAU,CAACI,KAAX,CAAiB,CAAjB,CAAV;AACH;;AACD;;AACJ;AACI;AAXR;AAaH;;AACDE,QAAAA,gBAAgB,CAACP,IAAD,EAAaC,UAAb,EAA8C;AAC1D,kBAAOA,UAAU,CAAClB,IAAlB;AACI;AACI;AAFR;AAIH;;AA/D2C,O", "sourcesContent": ["import { _decorator } from 'cc';\r\nconst { ccclass } = _decorator;\r\n\r\nimport Entity from 'db://assets/scripts/Game/ui/base/Entity';\r\nimport SkillComp from './skill/SkillComp';\r\nimport BuffComp, { Buff } from './skill/BuffComp';\r\nimport { builtin, res } from '../../../AutoGen/Luban/schema';\r\n\r\n\r\n@ccclass('FightEntity')\r\nexport default class FightEntity extends Entity {\r\n    enemy = true; // 是否为敌机\r\n    isDead = false; // 是否死亡\r\n    type = 0; // 敌人类型\r\n\r\n    maxHp:number = 0;\r\n    curHp:number = 0;\r\n    attack:number = 0;\r\n\r\n    private _skillComp: SkillComp|null = null;\r\n    private _buffComp: BuffComp|null = null;\r\n    \r\n    init() {\r\n        this._skillComp = new SkillComp();\r\n        this.addComp(\"skill\", this._skillComp);\r\n        this._buffComp = new BuffComp();\r\n        this.addComp(\"buff\", this._buffComp)\r\n        super.init();\r\n    }\r\n\r\n    get skillComp() {\r\n        return this._skillComp!;\r\n    }\r\n    \r\n    get buffComp() {\r\n        return this._buffComp!;\r\n    }\r\n\r\n    CastSkill(skillID: number) {\r\n        this.skillComp.Cast(this, skillID);\r\n    }\r\n\r\n    toDie():boolean {\r\n        if (this.isDead) {\r\n            return false\r\n        }\r\n        this.isDead = true;\r\n        return true\r\n    }\r\n\r\n    hurt(damage: number) {\r\n    }\r\n    \r\n    ApplyBuffEffect(buff: Buff|null, effectData: builtin.EffectParam) {\r\n        switch(effectData.type) {\r\n            case res.EffectType.Kill:\r\n                this.toDie();\r\n                break;\r\n            case res.EffectType.Hurt:\r\n                if (effectData.param.length >= 1)\r\n                {\r\n                    this.hurt(effectData.param[0]);\r\n                }\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n    RemoveBuffEffect(buff: Buff, effectData: builtin.EffectParam) {\r\n        switch(effectData.type) {\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n}"]}