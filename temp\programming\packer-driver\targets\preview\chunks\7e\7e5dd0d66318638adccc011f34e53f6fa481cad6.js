System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, Component, assetManager, EDITOR, BulletSystem, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _class, _class2, _descriptor, _class3, _crd, ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, EmitterEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../../scripts/Game/bullet/BulletSystem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
      Component = _cc.Component;
      assetManager = _cc.assetManager;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      BulletSystem = _unresolved_2.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6c1a6Gwu4lMHr2sSELvATS6", "EmitterEditor", undefined);

      __checkObsolete__(['_decorator', 'misc', 'instantiate', 'Node', 'Prefab', 'Component', 'JsonAsset', 'director', 'assetManager']);

      ({
        ccclass,
        playOnFocus,
        executeInEditMode,
        property,
        disallowMultiple,
        menu
      } = _decorator);

      _export("EmitterEditor", EmitterEditor = (_dec = ccclass('EmitterEditor'), _dec2 = menu('子弹系统/发射器编辑器'), _dec3 = playOnFocus(true), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        visible: false
      }), _dec7 = property({
        displayName: "目标帧率"
      }), _dec8 = property({
        displayName: "当前时间(ms)"
      }), _dec9 = property({
        displayName: "当前发射器数量"
      }), _dec10 = property({
        displayName: "当前子弹数量"
      }), _dec11 = property({
        displayName: "当前事件组数量"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = (_class3 = class EmitterEditor extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "fixedDelta", _descriptor, this);

          this._timeAccumulator = 0;
          this._updateInEditor = false;
        }

        // Fixed time step (e.g., 60 FPS), 单位: 毫秒
        get targetFrameRate() {
          return 1000 / this.fixedDelta;
        }

        set targetFrameRate(value) {
          this.fixedDelta = 1000 / value;
        }

        get frameTime() {
          return EmitterEditor.frameTimeInMilliseconds;
        }

        get emitterCount() {
          return (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).allEmitters.length;
        }

        get bulletCount() {
          return (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).allBullets.length;
        }

        get eventGroupCount() {
          return (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).allEventGroups.length;
        }

        resetInEditor() {
          this._updateInEditor = true; // console.log('resetInEditor');
        }

        onFocusInEditor() {
          this._updateInEditor = true; // console.log('onFocusInEditor');
          // @ts-ignore
          // Editor.Selection.select('node', BulletSystem.allEmitters.map(emitter => emitter.node.uuid));
        }

        onLostFocusInEditor() {
          this._updateInEditor = false; // this.reset();
        }

        start() {
          this.reset();
        }

        reset() {
          EmitterEditor.frameCount = 0;
          EmitterEditor.frameTimeInMilliseconds = 0;
          this._timeAccumulator = 0;
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).destroyAllBullets(true);

          for (var emitter of (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).allEmitters) {
            emitter.resetInEditor();
          }

          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).allEventGroups = [];
          console.log('reset: ', this._updateInEditor);
        }

        update(dt) {
          if (EDITOR && this._updateInEditor) {
            // this._timeAccumulator += dt * 1000;
            // if (this._timeAccumulator > 250) {
            //     this._timeAccumulator = 250;
            // }
            var milli_dt = dt * 1000; // console.log('Time Accumulator:', this._timeAccumulator, ', Fixed Delta:', this.fixedDelta, ', dt: ', dt);
            // while (this._timeAccumulator >= this.fixedDelta) {

            EmitterEditor.frameCount += 1;
            EmitterEditor.frameTimeInMilliseconds += milli_dt;
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).tick(milli_dt); // this._timeAccumulator -= this.fixedDelta;
            // console.log(`tick all at frame ${EmitterEditor.frameCount}`);
            // }
          }
        } // 编辑器方法


        instantiatePrefab(prefabUuid) {
          // replace db://assets/resources/Game/prefabs/emitter/ with assets/resources/Game/prefabs/emitter/
          //prefabUrl = prefabUrl.replace('db://', '');
          assetManager.loadAny({
            uuid: prefabUuid
          }, (err, prefab) => {
            if (err) {
              console.log('Failed to load prefab:', err);
              return;
            }

            var node = instantiate(prefab);
            var parent = this.node;
            parent.addChild(node);
          });
        } // public saveToPrefab(nodeUuid: string, prefabUrl: string): Promise<string> {
        //     console.log('saveToPrefab in Component:', nodeUuid, prefabUrl);        
        //     return new Promise<string>((resolve, reject) => {
        //         const scene = director.getScene();
        //         const target = scene!.getChildByUuid(nodeUuid);
        //         if (!target) {
        //             console.error("node not found:", nodeUuid);
        //             reject();
        //             return;
        //         }
        //         const json = cce.Utils.serialize(target);
        //         // 将节点保存为 Prefab
        //         // _utils.applyTargetOverrides(target as Node);
        //         // Editor.Message.request('asset-db', 'save-asset', prefabUrl, json);
        //         resolve(json);
        //     });
        // }


      }, _class3.frameCount = 0, _class3.frameTimeInMilliseconds = 0, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "fixedDelta", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 16.67;
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "targetFrameRate", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "targetFrameRate"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "frameTime", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "frameTime"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "emitterCount", [_dec9], Object.getOwnPropertyDescriptor(_class2.prototype, "emitterCount"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "bulletCount", [_dec10], Object.getOwnPropertyDescriptor(_class2.prototype, "bulletCount"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "eventGroupCount", [_dec11], Object.getOwnPropertyDescriptor(_class2.prototype, "eventGroupCount"), _class2.prototype)), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7e5dd0d66318638adccc011f34e53f6fa481cad6.js.map