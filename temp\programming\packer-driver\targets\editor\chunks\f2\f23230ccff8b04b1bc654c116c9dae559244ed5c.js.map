{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts"], "names": ["Color", "Vec3", "GizmoDrawer", "RegisterGizmoDrawer", "Gizmo<PERSON><PERSON>s", "Emitter", "EmitterGizmo", "componentType", "drawerName", "showRadius", "showDirections", "showCenter", "showArc", "radiusColor", "GRAY", "directionColor", "RED", "centerColor", "WHITE", "arcColor", "YELLOW", "speedScale", "arrowSize", "centerSize", "disableCameraScaling", "drawGizmos", "emitter", "graphics", "node", "gizmoPos", "worldToGizmoSpace", "worldPosition", "gizmoX", "x", "gizmoY", "y", "cameraScale", "getCameraZoomScale", "drawCenter", "radius", "value", "drawRadius", "arc", "drawArcIndicator", "count", "drawDirections", "worldPos", "gizmoNode", "localPos", "inverseTransformPoint", "worldX", "worldY", "drawCross", "drawCrossScaled", "error", "drawCircle", "drawCircleScaled", "strokeColor", "lineWidth", "baseAngleRad", "angle", "Math", "PI", "arcRad", "startAngle", "endAngle", "arcStartRadius", "baseLength", "speedFactor", "emitPower", "<PERSON><PERSON><PERSON><PERSON>", "max", "arcEndRadius", "segments", "floor", "i", "cos", "sin", "moveTo", "lineTo", "startSpawnX", "startSpawnY", "endSpawnX", "endSpawnY", "startEndX", "startEndY", "endEndX", "endEndY", "stroke", "<PERSON><PERSON><PERSON><PERSON>", "direction", "getSpawnDirection", "spawnPos", "getSpawnPosition", "startX", "startY", "endX", "endY", "drawArrowScaled", "getPriority", "configure", "options", "undefined", "disableAllCameraScaling", "GizmoManager", "require", "drawer", "get<PERSON>rawer", "enableAllCameraScaling"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA+BA,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;;AACnCC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,mB,iBAAAA,mB;;AACbC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;AAET;AACA;AACA;AACA;8BAEaC,Y;;oEADb,MACaA,YADb;AAAA;AAAA,sCACuD;AAAA;AAAA;AAAA,eAEnCC,aAFmC;AAAA;AAAA;AAAA,eAGnCC,UAHmC,GAGtB,cAHsB;AAKnD;AALmD,eAM5CC,UAN4C,GAMtB,IANsB;AAAA,eAO5CC,cAP4C,GAOlB,IAPkB;AAAA,eAQ5CC,UAR4C,GAQtB,IARsB;AAAA,eAS5CC,OAT4C,GASzB,IATyB;AAWnD;AAXmD,eAY5CC,WAZ4C,GAYvBb,KAAK,CAACc,IAZiB;AAAA,eAa5CC,cAb4C,GAapBf,KAAK,CAACgB,GAbc;AAAA,eAc5CC,WAd4C,GAcvBjB,KAAK,CAACkB,KAdiB;AAAA,eAe5CC,QAf4C,GAe1BnB,KAAK,CAACoB,MAfoB;AAiBnD;AAjBmD,eAkB5CC,UAlB4C,GAkBvB,GAlBuB;AAAA,eAmB5CC,SAnB4C,GAmBxB,CAnBwB;AAAA,eAoB5CC,UApB4C,GAoBvB,CApBuB;AAsBnD;AAtBmD,eAuB5CC,oBAvB4C,GAuBZ,KAvBY;AAAA;;AAyB5CC,QAAAA,UAAU,CAACC,OAAD,EAAmBC,QAAnB,EAAuCC,IAAvC,EAAyD;AACtE;AACA,gBAAMC,QAAQ,GAAG,KAAKC,iBAAL,CAAuBF,IAAI,CAACG,aAA5B,EAA2CJ,QAAQ,CAACC,IAApD,CAAjB;AACA,gBAAMI,MAAM,GAAGH,QAAQ,CAACI,CAAxB;AACA,gBAAMC,MAAM,GAAGL,QAAQ,CAACM,CAAxB,CAJsE,CAMtE;;AACA,gBAAMC,WAAW,GAAG;AAAA;AAAA,wCAAWC,kBAAX,EAApB,CAPsE,CAStE;;AACA,cAAI,KAAK1B,UAAT,EAAqB;AACjB,iBAAK2B,UAAL,CAAgBX,QAAhB,EAA0BK,MAA1B,EAAkCE,MAAlC;AACH,WAZqE,CActE;;;AACA,cAAI,KAAKzB,UAAL,IAAmBiB,OAAO,CAACa,MAAR,CAAeC,KAAf,GAAuB,CAA9C,EAAiD;AAC7C,iBAAKC,UAAL,CAAgBd,QAAhB,EAA0BK,MAA1B,EAAkCE,MAAlC,EAA0CR,OAAO,CAACa,MAAR,CAAeC,KAAzD;AACH,WAjBqE,CAmBtE;;;AACA,cAAI,KAAK5B,OAAL,IAAgBc,OAAO,CAACgB,GAAR,CAAYF,KAAZ,GAAoB,CAAxC,EAA2C;AACvC,iBAAKG,gBAAL,CAAsBhB,QAAtB,EAAgCD,OAAhC,EAAyCM,MAAzC,EAAiDE,MAAjD,EAAyDE,WAAzD;AACH,WAtBqE,CAwBtE;;;AACA,cAAI,KAAK1B,cAAL,IAAuBgB,OAAO,CAACkB,KAAR,CAAcJ,KAAd,GAAsB,CAAjD,EAAoD;AAChD,iBAAKK,cAAL,CAAoBlB,QAApB,EAA8BD,OAA9B,EAAuCM,MAAvC,EAA+CE,MAA/C,EAAuDE,WAAvD;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACYN,QAAAA,iBAAiB,CAACgB,QAAD,EAA2BC,SAA3B,EAAsE;AAC3F;AACA,gBAAMC,QAAQ,GAAG,IAAI/C,IAAJ,EAAjB;AACA8C,UAAAA,SAAS,CAACE,qBAAV,CAAgCD,QAAhC,EAA0CF,QAA1C;AACA,iBAAO;AAAEb,YAAAA,CAAC,EAAEe,QAAQ,CAACf,CAAd;AAAiBE,YAAAA,CAAC,EAAEa,QAAQ,CAACb;AAA7B,WAAP;AACH;;AAEOG,QAAAA,UAAU,CAACX,QAAD,EAAqBuB,MAArB,EAAqCC,MAArC,EAA2D;AACzE,cAAI,KAAK3B,oBAAT,EAA+B;AAC3B;AAAA;AAAA,0CAAW4B,SAAX,CAAqBzB,QAArB,EAA+BuB,MAA/B,EAAuCC,MAAvC,EAA+C,KAAK5B,UAApD,EAAgE,KAAKN,WAArE;AACA;AACH;;AAED,cAAI;AACA;AAAA;AAAA,0CAAWoC,eAAX,CAA2B1B,QAA3B,EAAqCuB,MAArC,EAA6CC,MAA7C,EAAqD,KAAK5B,UAA1D,EAAsE,KAAKN,WAA3E;AACH,WAFD,CAEE,OAAOqC,KAAP,EAAc;AACZ;AAAA;AAAA,0CAAWF,SAAX,CAAqBzB,QAArB,EAA+BuB,MAA/B,EAAuCC,MAAvC,EAA+C,KAAK5B,UAApD,EAAgE,KAAKN,WAArE;AACH;AACJ;;AAEOwB,QAAAA,UAAU,CAACd,QAAD,EAAqBuB,MAArB,EAAqCC,MAArC,EAAqDZ,MAArD,EAA2E;AACzF,cAAI,KAAKf,oBAAT,EAA+B;AAC3B;AAAA;AAAA,0CAAW+B,UAAX,CAAsB5B,QAAtB,EAAgCuB,MAAhC,EAAwCC,MAAxC,EAAgDZ,MAAhD,EAAwD,KAAK1B,WAA7D,EAA0E,KAA1E;AACA;AACH;;AAED,cAAI;AACA;AAAA;AAAA,0CAAW2C,gBAAX,CAA4B7B,QAA5B,EAAsCuB,MAAtC,EAA8CC,MAA9C,EAAsDZ,MAAtD,EAA8D,KAAK1B,WAAnE,EAAgF,KAAhF;AACH,WAFD,CAEE,OAAOyC,KAAP,EAAc;AACZ;AAAA;AAAA,0CAAWC,UAAX,CAAsB5B,QAAtB,EAAgCuB,MAAhC,EAAwCC,MAAxC,EAAgDZ,MAAhD,EAAwD,KAAK1B,WAA7D,EAA0E,KAA1E;AACH;AACJ;;AAEO8B,QAAAA,gBAAgB,CAAChB,QAAD,EAAqBD,OAArB,EAAuCwB,MAAvC,EAAuDC,MAAvD,EAAuEf,WAAvE,EAAkG;AACtH,cAAIV,OAAO,CAACgB,GAAR,CAAYF,KAAZ,IAAqB,CAAzB,EAA4B;AAE5Bb,UAAAA,QAAQ,CAAC8B,WAAT,GAAuB,KAAKtC,QAA5B;AACAQ,UAAAA,QAAQ,CAAC+B,SAAT,GAAqB,IAAItB,WAAzB,CAJsH,CAMtH;AACA;;AACA,gBAAMuB,YAAY,GAAGjC,OAAO,CAACkC,KAAR,CAAcpB,KAAd,GAAsBqB,IAAI,CAACC,EAA3B,GAAgC,GAArD;AACA,gBAAMC,MAAM,GAAGrC,OAAO,CAACgB,GAAR,CAAYF,KAAZ,GAAoBqB,IAAI,CAACC,EAAzB,GAA8B,GAA7C;AAEA,gBAAME,UAAU,GAAGL,YAAY,GAAGI,MAAM,GAAG,CAA3C;AACA,gBAAME,QAAQ,GAAGN,YAAY,GAAGI,MAAM,GAAG,CAAzC,CAZsH,CActH;;AACA,gBAAMG,cAAc,GAAGxC,OAAO,CAACa,MAAR,CAAeC,KAAtC,CAfsH,CAezE;AAC7C;;AACA,gBAAM2B,UAAU,GAAG,KAAK/B,WAAxB,CAjBsH,CAiBjF;;AACrC,gBAAMgC,WAAW,GAAG1C,OAAO,CAAC2C,SAAR,CAAkB7B,KAAlB,IAA2B,CAA/C;AACA,gBAAM8B,SAAS,GAAGT,IAAI,CAACU,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAK/C,UAArD,CAAlB;AACA,gBAAMmD,YAAY,GAAGN,cAAc,GAAGI,SAAtC,CApBsH,CAsBtH;;AACA,gBAAMG,QAAQ,GAAGZ,IAAI,CAACU,GAAL,CAAS,CAAT,EAAYV,IAAI,CAACa,KAAL,CAAWhD,OAAO,CAACgB,GAAR,CAAYF,KAAZ,GAAoB,CAA/B,CAAZ,CAAjB,CAvBsH,CAuBrD;AAEjE;;AACA,eAAK,IAAImC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIF,QAArB,EAA+BE,CAAC,EAAhC,EAAoC;AAChC,kBAAMf,KAAK,GAAGI,UAAU,GAAG,CAACC,QAAQ,GAAGD,UAAZ,KAA2BW,CAAC,GAAGF,QAA/B,CAA3B;AACA,kBAAMxC,CAAC,GAAGiB,MAAM,GAAGW,IAAI,CAACe,GAAL,CAAShB,KAAT,IAAkBY,YAArC;AACA,kBAAMrC,CAAC,GAAGgB,MAAM,GAAGU,IAAI,CAACgB,GAAL,CAASjB,KAAT,IAAkBY,YAArC;;AAEA,gBAAIG,CAAC,KAAK,CAAV,EAAa;AACThD,cAAAA,QAAQ,CAACmD,MAAT,CAAgB7C,CAAhB,EAAmBE,CAAnB;AACH,aAFD,MAEO;AACHR,cAAAA,QAAQ,CAACoD,MAAT,CAAgB9C,CAAhB,EAAmBE,CAAnB;AACH;AACJ,WApCqH,CAsCtH;;;AACA,gBAAM6C,WAAW,GAAG9B,MAAM,GAAGW,IAAI,CAACe,GAAL,CAASZ,UAAT,IAAuBE,cAApD;AACA,gBAAMe,WAAW,GAAG9B,MAAM,GAAGU,IAAI,CAACgB,GAAL,CAASb,UAAT,IAAuBE,cAApD;AACA,gBAAMgB,SAAS,GAAGhC,MAAM,GAAGW,IAAI,CAACe,GAAL,CAASX,QAAT,IAAqBC,cAAhD;AACA,gBAAMiB,SAAS,GAAGhC,MAAM,GAAGU,IAAI,CAACgB,GAAL,CAASZ,QAAT,IAAqBC,cAAhD;AAEA,gBAAMkB,SAAS,GAAGlC,MAAM,GAAGW,IAAI,CAACe,GAAL,CAASZ,UAAT,IAAuBQ,YAAlD;AACA,gBAAMa,SAAS,GAAGlC,MAAM,GAAGU,IAAI,CAACgB,GAAL,CAASb,UAAT,IAAuBQ,YAAlD;AACA,gBAAMc,OAAO,GAAGpC,MAAM,GAAGW,IAAI,CAACe,GAAL,CAASX,QAAT,IAAqBO,YAA9C;AACA,gBAAMe,OAAO,GAAGpC,MAAM,GAAGU,IAAI,CAACgB,GAAL,CAASZ,QAAT,IAAqBO,YAA9C,CA/CsH,CAiDtH;;AACA7C,UAAAA,QAAQ,CAACmD,MAAT,CAAgBE,WAAhB,EAA6BC,WAA7B;AACAtD,UAAAA,QAAQ,CAACoD,MAAT,CAAgBK,SAAhB,EAA2BC,SAA3B;AACA1D,UAAAA,QAAQ,CAACmD,MAAT,CAAgBI,SAAhB,EAA2BC,SAA3B;AACAxD,UAAAA,QAAQ,CAACoD,MAAT,CAAgBO,OAAhB,EAAyBC,OAAzB;AAEA5D,UAAAA,QAAQ,CAAC6D,MAAT;AACH;;AAEO3C,QAAAA,cAAc,CAAClB,QAAD,EAAqBD,OAArB,EAAuCwB,MAAvC,EAAuDC,MAAvD,EAAuEf,WAAvE,EAAkG;AACpH,gBAAM+B,UAAU,GAAG,KAAK/B,WAAxB,CADoH,CAC/E;;AACrC,gBAAMgC,WAAW,GAAG1C,OAAO,CAAC2C,SAAR,CAAkB7B,KAAlB,IAA2B,CAA/C;AACA,gBAAMiD,WAAW,GAAG5B,IAAI,CAACU,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAK/C,UAArD,CAApB;;AAEA,eAAK,IAAIsD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGjD,OAAO,CAACkB,KAAR,CAAcJ,KAAlC,EAAyCmC,CAAC,EAA1C,EAA8C;AAC1C,kBAAMe,SAAS,GAAGhE,OAAO,CAACiE,iBAAR,CAA0BhB,CAA1B,CAAlB;AACA,kBAAMiB,QAAQ,GAAGlE,OAAO,CAACmE,gBAAR,CAAyBlB,CAAzB,EAA4B,CAA5B,CAAjB,CAF0C,CAI1C;;AACA,kBAAMmB,MAAM,GAAG5C,MAAM,GAAG0C,QAAQ,CAAC3D,CAAjC;AACA,kBAAM8D,MAAM,GAAG5C,MAAM,GAAGyC,QAAQ,CAACzD,CAAjC,CAN0C,CAQ1C;;AACA,kBAAM6D,IAAI,GAAGF,MAAM,GAAGJ,SAAS,CAACzD,CAAV,GAAcwD,WAApC;AACA,kBAAMQ,IAAI,GAAGF,MAAM,GAAGL,SAAS,CAACvD,CAAV,GAAcsD,WAApC,CAV0C,CAY1C;;AACA;AAAA;AAAA,0CAAWS,eAAX,CAA2BvE,QAA3B,EAAqCmE,MAArC,EAA6CC,MAA7C,EAAqDC,IAArD,EAA2DC,IAA3D,EAAiE,KAAKlF,cAAtE,EAAsF,KAAKO,SAA3F;AACH;AACJ;;AAEM6E,QAAAA,WAAW,GAAW;AACzB,iBAAO,EAAP,CADyB,CACd;AACd;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACC,OAAD,EAaP;AACL,cAAIA,OAAO,CAAC5F,UAAR,KAAuB6F,SAA3B,EAAsC,KAAK7F,UAAL,GAAkB4F,OAAO,CAAC5F,UAA1B;AACtC,cAAI4F,OAAO,CAAC3F,cAAR,KAA2B4F,SAA/B,EAA0C,KAAK5F,cAAL,GAAsB2F,OAAO,CAAC3F,cAA9B;AAC1C,cAAI2F,OAAO,CAAC1F,UAAR,KAAuB2F,SAA3B,EAAsC,KAAK3F,UAAL,GAAkB0F,OAAO,CAAC1F,UAA1B;AACtC,cAAI0F,OAAO,CAACzF,OAAR,KAAoB0F,SAAxB,EAAmC,KAAK1F,OAAL,GAAeyF,OAAO,CAACzF,OAAvB;AACnC,cAAIyF,OAAO,CAACxF,WAAR,KAAwByF,SAA5B,EAAuC,KAAKzF,WAAL,GAAmBwF,OAAO,CAACxF,WAA3B;AACvC,cAAIwF,OAAO,CAACtF,cAAR,KAA2BuF,SAA/B,EAA0C,KAAKvF,cAAL,GAAsBsF,OAAO,CAACtF,cAA9B;AAC1C,cAAIsF,OAAO,CAACpF,WAAR,KAAwBqF,SAA5B,EAAuC,KAAKrF,WAAL,GAAmBoF,OAAO,CAACpF,WAA3B;AACvC,cAAIoF,OAAO,CAAClF,QAAR,KAAqBmF,SAAzB,EAAoC,KAAKnF,QAAL,GAAgBkF,OAAO,CAAClF,QAAxB;AACpC,cAAIkF,OAAO,CAAChF,UAAR,KAAuBiF,SAA3B,EAAsC,KAAKjF,UAAL,GAAkBgF,OAAO,CAAChF,UAA1B;AACtC,cAAIgF,OAAO,CAAC/E,SAAR,KAAsBgF,SAA1B,EAAqC,KAAKhF,SAAL,GAAiB+E,OAAO,CAAC/E,SAAzB;AACrC,cAAI+E,OAAO,CAAC9E,UAAR,KAAuB+E,SAA3B,EAAsC,KAAK/E,UAAL,GAAkB8E,OAAO,CAAC9E,UAA1B;AACtC,cAAI8E,OAAO,CAAC7E,oBAAR,KAAiC8E,SAArC,EAAgD,KAAK9E,oBAAL,GAA4B6E,OAAO,CAAC7E,oBAApC;AACnD;AAED;AACJ;AACA;AACA;;;AACyC,eAAvB+E,uBAAuB,GAAS;AAC1C,cAAI;AACA,kBAAM;AAAEC,cAAAA;AAAF,gBAAmBC,OAAO,CAAC,gBAAD,CAAhC;;AACA,kBAAMC,MAAM,GAAGF,YAAY,CAACG,SAAb,CAAuB,cAAvB,CAAf;;AACA,gBAAID,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAAClF,oBAAP,GAA8B,IAA9B;AACH;AACJ,WAND,CAME,OAAO8B,KAAP,EAAc,CACZ;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACwC,eAAtBsD,sBAAsB,GAAS;AACzC,cAAI;AACA,kBAAM;AAAEJ,cAAAA;AAAF,gBAAmBC,OAAO,CAAC,gBAAD,CAAhC;;AACA,kBAAMC,MAAM,GAAGF,YAAY,CAACG,SAAb,CAAuB,cAAvB,CAAf;;AACA,gBAAID,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAAClF,oBAAP,GAA8B,KAA9B;AACH;AACJ,WAND,CAME,OAAO8B,KAAP,EAAc,CACZ;AACH;AACJ;;AA7OkD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node, Vec3, Camera } from 'cc';\nimport { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';\nimport { GizmoUtils } from './GizmoUtils';\nimport { Emitter } from 'db://assets/scripts/Game/bullet/Emitter';\n\n/**\n * Gizmo drawer for EmitterArc components\n * Draws visual debugging information for arc-based bullet emitters\n */\n@RegisterGizmoDrawer\nexport class EmitterGizmo extends GizmoDrawer<Emitter> {\n    \n    public readonly componentType = Emitter;\n    public readonly drawerName = \"EmitterGizmo\";\n    \n    // Gizmo display options\n    public showRadius: boolean = true;\n    public showDirections: boolean = true;\n    public showCenter: boolean = true;\n    public showArc: boolean = true;\n    \n    // Colors\n    public radiusColor: Color = Color.GRAY;\n    public directionColor: Color = Color.RED;\n    public centerColor: Color = Color.WHITE;\n    public arcColor: Color = Color.YELLOW;\n    \n    // Display settings\n    public speedScale: number = 1.0;\n    public arrowSize: number = 8;\n    public centerSize: number = 8;\n\n    // Debug option to disable camera scaling\n    public disableCameraScaling: boolean = false;\n    \n    public drawGizmos(emitter: Emitter, graphics: Graphics, node: Node): void {\n        // For 2D projects, convert world position to graphics local space\n        const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);\n        const gizmoX = gizmoPos.x;\n        const gizmoY = gizmoPos.y;\n\n        // Calculate camera zoom scale to keep gizmo size consistent\n        const cameraScale = GizmoUtils.getCameraZoomScale();\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter(graphics, gizmoX, gizmoY);\n        }\n\n        // Draw radius circle\n        if (this.showRadius && emitter.radius.value > 0) {\n            this.drawRadius(graphics, gizmoX, gizmoY, emitter.radius.value);\n        }\n\n        // Draw arc indicator\n        if (this.showArc && emitter.arc.value > 0) {\n            this.drawArcIndicator(graphics, emitter, gizmoX, gizmoY, cameraScale);\n        }\n\n        // Draw direction arrows\n        if (this.showDirections && emitter.count.value > 0) {\n            this.drawDirections(graphics, emitter, gizmoX, gizmoY, cameraScale);\n        }\n    }\n\n    /**\n     * Convert world position to gizmo graphics coordinate space\n     * For 2D projects, this converts world coordinates to the local space of the graphics node\n     */\n    private worldToGizmoSpace(worldPos: Readonly<Vec3>, gizmoNode: Node): { x: number, y: number } {\n        // Convert world position to local position of the gizmo graphics node\n        const localPos = new Vec3();\n        gizmoNode.inverseTransformPoint(localPos, worldPos);\n        return { x: localPos.x, y: localPos.y };\n    }\n\n    private drawCenter(graphics: Graphics, worldX: number, worldY: number): void {\n        if (this.disableCameraScaling) {\n            GizmoUtils.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);\n            return;\n        }\n\n        try {\n            GizmoUtils.drawCrossScaled(graphics, worldX, worldY, this.centerSize, this.centerColor);\n        } catch (error) {\n            GizmoUtils.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);\n        }\n    }\n\n    private drawRadius(graphics: Graphics, worldX: number, worldY: number, radius: number): void {\n        if (this.disableCameraScaling) {\n            GizmoUtils.drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);\n            return;\n        }\n\n        try {\n            GizmoUtils.drawCircleScaled(graphics, worldX, worldY, radius, this.radiusColor, false);\n        } catch (error) {\n            GizmoUtils.drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);\n        }\n    }\n    \n    private drawArcIndicator(graphics: Graphics, emitter: Emitter, worldX: number, worldY: number, cameraScale: number): void {\n        if (emitter.arc.value <= 0) return;\n\n        graphics.strokeColor = this.arcColor;\n        graphics.lineWidth = 2 * cameraScale;\n\n        // Convert angle and arc to radians\n        // Use the same coordinate system as EmitterArc.getDirection() - no +90 offset\n        const baseAngleRad = emitter.angle.value * Math.PI / 180;\n        const arcRad = emitter.arc.value * Math.PI / 180;\n\n        const startAngle = baseAngleRad - arcRad / 2;\n        const endAngle = baseAngleRad + arcRad / 2;\n\n        // Draw arc starting from the emitter radius (spawn position) extending outward\n        const arcStartRadius = emitter.radius.value; // Start from spawn radius\n        // Use same length calculation as direction arrows for consistency\n        const baseLength = 30 * cameraScale; // Scale the base length\n        const speedFactor = emitter.emitPower.value || 1;\n        const arcLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n        const arcEndRadius = arcStartRadius + arcLength;\n\n        // Draw arc lines from spawn radius extending outward to show direction range\n        const segments = Math.max(8, Math.floor(emitter.arc.value / 5)); // More segments for larger arcs\n\n        // Draw the arc at the end radius to show the direction spread\n        for (let i = 0; i <= segments; i++) {\n            const angle = startAngle + (endAngle - startAngle) * (i / segments);\n            const x = worldX + Math.cos(angle) * arcEndRadius;\n            const y = worldY + Math.sin(angle) * arcEndRadius;\n\n            if (i === 0) {\n                graphics.moveTo(x, y);\n            } else {\n                graphics.lineTo(x, y);\n            }\n        }\n\n        // Draw lines from spawn position to end of arc to show the direction range\n        const startSpawnX = worldX + Math.cos(startAngle) * arcStartRadius;\n        const startSpawnY = worldY + Math.sin(startAngle) * arcStartRadius;\n        const endSpawnX = worldX + Math.cos(endAngle) * arcStartRadius;\n        const endSpawnY = worldY + Math.sin(endAngle) * arcStartRadius;\n\n        const startEndX = worldX + Math.cos(startAngle) * arcEndRadius;\n        const startEndY = worldY + Math.sin(startAngle) * arcEndRadius;\n        const endEndX = worldX + Math.cos(endAngle) * arcEndRadius;\n        const endEndY = worldY + Math.sin(endAngle) * arcEndRadius;\n\n        // Draw lines from spawn radius to end radius for arc boundaries\n        graphics.moveTo(startSpawnX, startSpawnY);\n        graphics.lineTo(startEndX, startEndY);\n        graphics.moveTo(endSpawnX, endSpawnY);\n        graphics.lineTo(endEndX, endEndY);\n\n        graphics.stroke();\n    }\n    \n    private drawDirections(graphics: Graphics, emitter: Emitter, worldX: number, worldY: number, cameraScale: number): void {\n        const baseLength = 30 * cameraScale; // Scale the base length\n        const speedFactor = emitter.emitPower.value || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n        for (let i = 0; i < emitter.count.value; i++) {\n            const direction = emitter.getSpawnDirection(i);\n            const spawnPos = emitter.getSpawnPosition(i, 0);\n\n            // Start position (at spawn position relative to world position)\n            const startX = worldX + spawnPos.x;\n            const startY = worldY + spawnPos.y;\n\n            // End position (direction from spawn position)\n            const endX = startX + direction.x * arrowLength;\n            const endY = startY + direction.y * arrowLength;\n\n            // Draw arrow with scaled size\n            GizmoUtils.drawArrowScaled(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);\n        }\n    }\n    \n    public getPriority(): number {\n        return 10; // Draw emitter gizmos with medium priority\n    }\n    \n    /**\n     * Configure display options\n     */\n    public configure(options: {\n        showRadius?: boolean;\n        showDirections?: boolean;\n        showCenter?: boolean;\n        showArc?: boolean;\n        radiusColor?: Color;\n        directionColor?: Color;\n        centerColor?: Color;\n        arcColor?: Color;\n        speedScale?: number;\n        arrowSize?: number;\n        centerSize?: number;\n        disableCameraScaling?: boolean;\n    }): void {\n        if (options.showRadius !== undefined) this.showRadius = options.showRadius;\n        if (options.showDirections !== undefined) this.showDirections = options.showDirections;\n        if (options.showCenter !== undefined) this.showCenter = options.showCenter;\n        if (options.showArc !== undefined) this.showArc = options.showArc;\n        if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;\n        if (options.directionColor !== undefined) this.directionColor = options.directionColor;\n        if (options.centerColor !== undefined) this.centerColor = options.centerColor;\n        if (options.arcColor !== undefined) this.arcColor = options.arcColor;\n        if (options.speedScale !== undefined) this.speedScale = options.speedScale;\n        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;\n        if (options.centerSize !== undefined) this.centerSize = options.centerSize;\n        if (options.disableCameraScaling !== undefined) this.disableCameraScaling = options.disableCameraScaling;\n    }\n\n    /**\n     * Quick method to disable camera scaling for troubleshooting\n     * Call this from the console: EmitterGizmo.disableAllCameraScaling()\n     */\n    public static disableAllCameraScaling(): void {\n        try {\n            const { GizmoManager } = require('./GizmoManager');\n            const drawer = GizmoManager.getDrawer(\"EmitterGizmo\") as EmitterGizmo;\n            if (drawer) {\n                drawer.disableCameraScaling = true;\n            }\n        } catch (error) {\n            // Silent fallback\n        }\n    }\n\n    /**\n     * Quick method to re-enable camera scaling\n     * Call this from the console: EmitterGizmo.enableAllCameraScaling()\n     */\n    public static enableAllCameraScaling(): void {\n        try {\n            const { GizmoManager } = require('./GizmoManager');\n            const drawer = GizmoManager.getDrawer(\"EmitterGizmo\") as EmitterGizmo;\n            if (drawer) {\n                drawer.disableCameraScaling = false;\n            }\n        } catch (error) {\n            // Silent fallback\n        }\n    }\n}\n"]}