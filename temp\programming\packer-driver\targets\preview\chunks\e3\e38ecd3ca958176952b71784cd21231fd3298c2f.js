System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Entity, Skill<PERSON>omp, Buff<PERSON>omp, res, _dec, _class, _crd, ccclass, FightEntity;

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/scripts/Game/ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkillComp(extras) {
    _reporterNs.report("SkillComp", "./skill/SkillComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBuffComp(extras) {
    _reporterNs.report("BuffComp", "./skill/BuffComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBuff(extras) {
    _reporterNs.report("Buff", "./skill/BuffComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfbuiltin(extras) {
    _reporterNs.report("builtin", "../../../AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfres(extras) {
    _reporterNs.report("res", "../../../AutoGen/Luban/schema", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      Entity = _unresolved_2.default;
    }, function (_unresolved_3) {
      SkillComp = _unresolved_3.default;
    }, function (_unresolved_4) {
      BuffComp = _unresolved_4.default;
    }, function (_unresolved_5) {
      res = _unresolved_5.res;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "725ee/n2elFjbkm8xLf6YsL", "FightEntity", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);

      _export("default", FightEntity = (_dec = ccclass('FightEntity'), _dec(_class = class FightEntity extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super(...arguments);
          this.enemy = true;
          // 是否为敌机
          this.isDead = false;
          // 是否死亡
          this.type = 0;
          // 敌人类型
          this.maxHp = 0;
          this.curHp = 0;
          this.attack = 0;
          this._skillComp = null;
          this._buffComp = null;
        }

        init() {
          this._skillComp = new (_crd && SkillComp === void 0 ? (_reportPossibleCrUseOfSkillComp({
            error: Error()
          }), SkillComp) : SkillComp)();
          this.addComp("skill", this._skillComp);
          this._buffComp = new (_crd && BuffComp === void 0 ? (_reportPossibleCrUseOfBuffComp({
            error: Error()
          }), BuffComp) : BuffComp)();
          this.addComp("buff", this._buffComp);
          super.init();
        }

        get skillComp() {
          return this._skillComp;
        }

        get buffComp() {
          return this._buffComp;
        }

        CastSkill(skillID) {
          this.skillComp.Cast(this, skillID);
        }

        toDie() {
          if (this.isDead) {
            return false;
          }

          this.isDead = true;
          return true;
        }

        hurt(damage) {}

        ApplyBuffEffect(buff, effectData) {
          switch (effectData.type) {
            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.Kill:
              this.toDie();
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).EffectType.Hurt:
              if (effectData.param.length >= 1) {
                this.hurt(effectData.param[0]);
              }

              break;

            default:
              break;
          }
        }

        RemoveBuffEffect(buff, effectData) {
          switch (effectData.type) {
            default:
              break;
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e38ecd3ca958176952b71784cd21231fd3298c2f.js.map