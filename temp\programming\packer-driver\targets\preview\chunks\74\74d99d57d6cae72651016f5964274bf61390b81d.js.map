{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/move/Movable.ts"], "names": ["_decorator", "misc", "Component", "Enum", "Vec3", "BulletSystem", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "eSpriteDefaultFacing", "Movable", "type", "displayName", "isFacingMoveDir", "isTrackingTarget", "speed", "speedAngle", "turnSpeed", "acceleration", "accelerationAngle", "target", "arrivalDistance", "_position", "_isVisible", "onBecomeVisible", "onBecomeInvisible", "tick", "dtInMiliseconds", "dt", "node", "getPosition", "velocityX", "Math", "cos", "velocityY", "sin", "targetPos", "currentPos", "directionX", "x", "directionY", "y", "distance", "sqrt", "desiredAngle", "atan2", "angleDiff", "normalizedAngleDiff", "trackingStrength", "maxTurnRate", "turnAmount", "min", "abs", "sign", "accelerationX", "accelerationY", "newVelocityX", "newVelocityY", "setPosition", "movementAngle", "finalAngle", "defaultFacing", "setRotationFromEuler", "checkVisibility", "visibleSize", "worldBounds", "isVisible", "xMin", "xMax", "yMin", "yMax", "setVisible", "visible", "<PERSON><PERSON><PERSON><PERSON>", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAInCC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAHH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCN,I;OACzC;AAAEO,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CV,U;;sCAIrCW,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;yBASCC,O,WAFZJ,OAAO,CAAC,SAAD,C,UAIHC,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEV,IAAI,CAACQ,oBAAD,CAAX;AAAmCG,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,gBAHZJ,iB,qBADD,MAEaE,OAFb,SAE6BV,SAF7B,CAE2D;AAAA;AAAA;;AAAA;;AAAA,eAKhDa,eALgD,GAKrB,KALqB;AAKT;AALS,eAMhDC,gBANgD,GAMpB,KANoB;AAMT;AANS,eAOhDC,KAPgD,GAOhC,CAPgC;AAOT;AAPS,eAQhDC,UARgD,GAQ3B,CAR2B;AAQT;AARS,eAShDC,SATgD,GAS5B,EAT4B;AAST;AATS,eAUhDC,YAVgD,GAUzB,CAVyB;AAUT;AAVS,eAWhDC,iBAXgD,GAWpB,CAXoB;AAWT;AAXS,eAahDC,MAbgD,GAa1B,IAb0B;AAaT;AAbS,eAchDC,eAdgD,GActB,EAdsB;AAcT;AAdS,eAgB/CC,SAhB+C,GAgB7B,IAAIpB,IAAJ,EAhB6B;AAAA,eAkB/CqB,UAlB+C,GAkBzB,IAlByB;AAkBT;AAlBS,eAmBhDC,eAnBgD,GAmBb,IAnBa;AAAA,eAoBhDC,iBApBgD,GAoBX,IApBW;AAAA;;AAsBhDC,QAAAA,IAAI,CAACC,eAAD,EAAgC;AACvC;AACA,cAAIC,EAAE,GAAGD,eAAe,GAAG,IAA3B,CAFuC,CAIvC;;AACA,eAAKE,IAAL,CAAUC,WAAV,CAAsB,KAAKR,SAA3B,EALuC,CAOvC;;AACA,cAAIS,SAAS,GAAG,KAAKhB,KAAL,GAAaiB,IAAI,CAACC,GAAL,CAAS7B,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAA7B;AACA,cAAIkB,SAAS,GAAG,KAAKnB,KAAL,GAAaiB,IAAI,CAACG,GAAL,CAAS/B,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAA7B;;AAEA,cAAI,KAAKF,gBAAL,IAAyB,KAAKM,MAAlC,EAA0C;AACtC,gBAAMgB,SAAS,GAAG,KAAKhB,MAAL,CAAYU,WAAZ,EAAlB;AACA,gBAAMO,UAAU,GAAG,KAAKR,IAAL,CAAUC,WAAV,EAAnB,CAFsC,CAItC;;AACA,gBAAMQ,UAAU,GAAGF,SAAS,CAACG,CAAV,GAAcF,UAAU,CAACE,CAA5C;AACA,gBAAMC,UAAU,GAAGJ,SAAS,CAACK,CAAV,GAAcJ,UAAU,CAACI,CAA5C;AACA,gBAAMC,QAAQ,GAAGV,IAAI,CAACW,IAAL,CAAUL,UAAU,GAAGA,UAAb,GAA0BE,UAAU,GAAGA,UAAjD,CAAjB;;AAEA,gBAAIE,QAAQ,GAAG,CAAf,EAAkB;AACd;AACA,kBAAME,YAAY,GAAGvC,gBAAgB,CAAC2B,IAAI,CAACa,KAAL,CAAWL,UAAX,EAAuBF,UAAvB,CAAD,CAArC,CAFc,CAId;;AACA,kBAAMQ,SAAS,GAAGF,YAAY,GAAG,KAAK5B,UAAtC,CALc,CAMd;;AACA,kBAAM+B,mBAAmB,GAAI,CAACD,SAAS,GAAG,GAAb,IAAoB,GAArB,GAA4B,GAAxD,CAPc,CASd;;AACA,kBAAME,gBAAgB,GAAG,GAAzB,CAVc,CAUgB;;AAC9B,kBAAMC,WAAW,GAAG,KAAKhC,SAAzB,CAXc,CAWsB;;AACpC,kBAAMiC,UAAU,GAAGlB,IAAI,CAACmB,GAAL,CAASnB,IAAI,CAACoB,GAAL,CAASL,mBAAT,CAAT,EAAwCE,WAAW,GAAGrB,EAAtD,IAA4DI,IAAI,CAACqB,IAAL,CAAUN,mBAAV,CAA/E;AAEA,mBAAK/B,UAAL,IAAmBkC,UAAU,GAAGF,gBAAhC,CAdc,CAgBd;;AACAjB,cAAAA,SAAS,GAAG,KAAKhB,KAAL,GAAaiB,IAAI,CAACC,GAAL,CAAS7B,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACAkB,cAAAA,SAAS,GAAG,KAAKnB,KAAL,GAAaiB,IAAI,CAACG,GAAL,CAAS/B,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACH;AACJ,WAxCsC,CA0CvC;;;AACA,cAAMsC,aAAa,GAAG,KAAKpC,YAAL,GAAoBc,IAAI,CAACC,GAAL,CAAS7B,gBAAgB,CAAC,KAAKe,iBAAN,CAAzB,CAA1C;AACA,cAAMoC,aAAa,GAAG,KAAKrC,YAAL,GAAoBc,IAAI,CAACG,GAAL,CAAS/B,gBAAgB,CAAC,KAAKe,iBAAN,CAAzB,CAA1C,CA5CuC,CA8CvC;;AACA,cAAMqC,YAAY,GAAGzB,SAAS,GAAGuB,aAAa,GAAG1B,EAAjD;AACA,cAAM6B,YAAY,GAAGvB,SAAS,GAAGqB,aAAa,GAAG3B,EAAjD,CAhDuC,CAkDvC;;AACA,eAAKb,KAAL,GAAaiB,IAAI,CAACW,IAAL,CAAUa,YAAY,GAAGA,YAAf,GAA8BC,YAAY,GAAGA,YAAvD,CAAb;AACA,eAAKzC,UAAL,GAAkBX,gBAAgB,CAAC2B,IAAI,CAACa,KAAL,CAAWY,YAAX,EAAyBD,YAAzB,CAAD,CAAlC,CApDuC,CAsDvC;;AACA,eAAKlC,SAAL,CAAeiB,CAAf,IAAoBiB,YAAY,GAAG5B,EAAnC;AACA,eAAKN,SAAL,CAAemB,CAAf,IAAoBgB,YAAY,GAAG7B,EAAnC;AACA,eAAKC,IAAL,CAAU6B,WAAV,CAAsB,KAAKpC,SAA3B;;AAEA,cAAI,KAAKT,eAAL,IAAwB,KAAKE,KAAL,GAAa,CAAzC,EAA4C;AACxC,gBAAM4C,aAAa,GAAGtD,gBAAgB,CAAC2B,IAAI,CAACa,KAAL,CAAWY,YAAX,EAAyBD,YAAzB,CAAD,CAAtC;AACA,gBAAMI,UAAU,GAAGD,aAAa,GAAG,KAAKE,aAAxC;AACA,iBAAKhC,IAAL,CAAUiC,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,UAArC;AACH;;AAED,eAAKG,eAAL;AACH;;AAEMA,QAAAA,eAAe,GAAS;AAC3B;AACA,cAAMC,WAAW,GAAG;AAAA;AAAA,4CAAaC,WAAjC;AACA,cAAMC,SAAS,GAAG,KAAK5C,SAAL,CAAeiB,CAAf,IAAoByB,WAAW,CAACG,IAAhC,IAAwC,KAAK7C,SAAL,CAAeiB,CAAf,IAAoByB,WAAW,CAACI,IAAxE,IACA,KAAK9C,SAAL,CAAemB,CAAf,IAAoBuB,WAAW,CAACK,IADhC,IACwC,KAAK/C,SAAL,CAAemB,CAAf,IAAoBuB,WAAW,CAACM,IAD1F;AAGA,eAAKC,UAAL,CAAgBL,SAAhB;AACH;;AAEMK,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAKjD,UAAL,KAAoBiD,OAAxB,EAAiC;AAEjC,eAAKjD,UAAL,GAAkBiD,OAAlB;;AACA,cAAIA,OAAO,IAAI,KAAKhD,eAApB,EAAqC;AACjC,iBAAKA,eAAL;AACH,WAFD,MAEO,IAAI,CAACgD,OAAD,IAAY,KAAK/C,iBAArB,EAAwC;AAC3C,iBAAKA,iBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACWgD,QAAAA,SAAS,CAACrD,MAAD,EAA4B;AACxC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKN,gBAAL,GAAwBM,MAAM,KAAK,IAAnC;AACH;;AApHsD,O;;;;;iBAGVX,oBAAoB,CAACiE,E", "sourcesContent": ["import { _decorator, misc, Component, Enum, Vec3, Node } from 'cc';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable } from './IMovable';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\n\r\nexport enum eSpriteDefaultFacing {\r\n    Right = 0,    // →\r\n    Up = -90,     // ↑\r\n    Down = 90,    // ↓\r\n    Left = 180    // ←\r\n}\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    @property({type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向'})\r\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\r\n\r\n    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向\r\n    public isTrackingTarget: boolean = false;     // 是否正在追踪目标\r\n    public speed: number = 1;                     // 速度\r\n    public speedAngle: number = 0;                // 速度方向 (用角度表示)\r\n    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）\r\n    public acceleration: number = 0;              // 加速度\r\n    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)\r\n\r\n    public target: Node | null = null;            // 追踪的目标节点\r\n    public arrivalDistance: number = 10;          // 到达目标的距离\r\n\r\n    private _position: Vec3 = new Vec3();\r\n\r\n    private _isVisible: boolean = true;           // 是否可见\r\n    public onBecomeVisible: Function | null = null;\r\n    public onBecomeInvisible: Function | null = null;\r\n\r\n    public tick(dtInMiliseconds: number): void {\r\n        // 毫秒 -> 秒\r\n        let dt = dtInMiliseconds / 1000;\r\n        \r\n        // 根据移动属性更新位置\r\n        this.node.getPosition(this._position);\r\n        \r\n        // Convert speed and angle to velocity vector\r\n        let velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n        let velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n\r\n        if (this.isTrackingTarget && this.target) {\r\n            const targetPos = this.target.getPosition();\r\n            const currentPos = this.node.getPosition();\r\n            \r\n            // Calculate direction to target\r\n            const directionX = targetPos.x - currentPos.x;\r\n            const directionY = targetPos.y - currentPos.y;\r\n            const distance = Math.sqrt(directionX * directionX + directionY * directionY);\r\n            \r\n            if (distance > 0) {\r\n                // Calculate desired angle to target\r\n                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));\r\n                \r\n                // Smoothly adjust speedAngle toward target\r\n                const angleDiff = desiredAngle - this.speedAngle;\r\n                // Normalize angle difference to [-180, 180] range\r\n                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;\r\n                \r\n                // Apply tracking adjustment (you can add a trackingStrength property to control this)\r\n                const trackingStrength = 1.0; // Can be made configurable\r\n                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable\r\n                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);\r\n                \r\n                this.speedAngle += turnAmount * trackingStrength;\r\n                \r\n                // Recalculate velocity with new angle\r\n                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n            }\r\n        }\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));\r\n        const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle));\r\n\r\n        // Update velocity vector: v = v + a * dt\r\n        const newVelocityX = velocityX + accelerationX * dt;\r\n        const newVelocityY = velocityY + accelerationY * dt;\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);\r\n        this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        this._position.x += newVelocityX * dt;\r\n        this._position.y += newVelocityY * dt;\r\n        this.node.setPosition(this._position);\r\n        \r\n        if (this.isFacingMoveDir && this.speed > 0) {\r\n            const movementAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n            const finalAngle = movementAngle + this.defaultFacing;\r\n            this.node.setRotationFromEuler(0, 0, finalAngle);\r\n        }\r\n\r\n        this.checkVisibility();\r\n    }\r\n\r\n    public checkVisibility(): void {\r\n        // check visibility by comparing with BulletSystem.worldBounds and this._position\r\n        const visibleSize = BulletSystem.worldBounds;\r\n        const isVisible = this._position.x >= visibleSize.xMin && this._position.x <= visibleSize.xMax &&\r\n                          this._position.y >= visibleSize.yMin && this._position.y <= visibleSize.yMax;\r\n\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this._isVisible === visible) return;\r\n\r\n        this._isVisible = visible;\r\n        if (visible && this.onBecomeVisible) {\r\n            this.onBecomeVisible();\r\n        } else if (!visible && this.onBecomeInvisible) {\r\n            this.onBecomeInvisible();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the target to track\r\n     */\r\n    public setTarget(target: Node | null): void {\r\n        this.target = target;\r\n        this.isTrackingTarget = target !== null;\r\n    }\r\n}"]}