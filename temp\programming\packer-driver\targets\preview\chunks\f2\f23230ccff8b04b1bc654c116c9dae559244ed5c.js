System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Color, Vec3, GizmoDrawer, RegisterGizmoDrawer, Giz<PERSON><PERSON><PERSON>s, Emitter, _dec, _class, _crd, EmitterGizmo;

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterGizmoDrawer(extras) {
    _reporterNs.report("RegisterGizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGizmoUtils(extras) {
    _reporterNs.report("GizmoUtils", "./GizmoUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "db://assets/scripts/Game/bullet/Emitter", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Color = _cc.Color;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GizmoDrawer = _unresolved_2.GizmoDrawer;
      RegisterGizmoDrawer = _unresolved_2.RegisterGizmoDrawer;
    }, function (_unresolved_3) {
      GizmoUtils = _unresolved_3.GizmoUtils;
    }, function (_unresolved_4) {
      Emitter = _unresolved_4.Emitter;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3c748JAhQlND5BFXS223daj", "EmitterGizmo", undefined);

      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Node', 'Vec3', 'Camera']);

      /**
       * Gizmo drawer for EmitterArc components
       * Draws visual debugging information for arc-based bullet emitters
       */
      _export("EmitterGizmo", EmitterGizmo = (_dec = _crd && RegisterGizmoDrawer === void 0 ? (_reportPossibleCrUseOfRegisterGizmoDrawer({
        error: Error()
      }), RegisterGizmoDrawer) : RegisterGizmoDrawer, _dec(_class = class EmitterGizmo extends (_crd && GizmoDrawer === void 0 ? (_reportPossibleCrUseOfGizmoDrawer({
        error: Error()
      }), GizmoDrawer) : GizmoDrawer) {
        constructor() {
          super(...arguments);
          this.componentType = _crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter;
          this.drawerName = "EmitterGizmo";
          // Gizmo display options
          this.showRadius = true;
          this.showDirections = true;
          this.showCenter = true;
          this.showArc = true;
          // Colors
          this.radiusColor = Color.GRAY;
          this.directionColor = Color.RED;
          this.centerColor = Color.WHITE;
          this.arcColor = Color.YELLOW;
          // Display settings
          this.speedScale = 1.0;
          this.arrowSize = 8;
          this.centerSize = 8;
          // Debug option to disable camera scaling
          this.disableCameraScaling = false;
        }

        drawGizmos(emitter, graphics, node) {
          // For 2D projects, convert world position to graphics local space
          var gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);
          var gizmoX = gizmoPos.x;
          var gizmoY = gizmoPos.y; // Calculate camera zoom scale to keep gizmo size consistent

          var cameraScale = (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
            error: Error()
          }), GizmoUtils) : GizmoUtils).getCameraZoomScale(); // Draw center point

          if (this.showCenter) {
            this.drawCenter(graphics, gizmoX, gizmoY);
          } // Draw radius circle


          if (this.showRadius && emitter.radius.value > 0) {
            this.drawRadius(graphics, gizmoX, gizmoY, emitter.radius.value);
          } // Draw arc indicator


          if (this.showArc && emitter.arc.value > 0) {
            this.drawArcIndicator(graphics, emitter, gizmoX, gizmoY, cameraScale);
          } // Draw direction arrows


          if (this.showDirections && emitter.count.value > 0) {
            this.drawDirections(graphics, emitter, gizmoX, gizmoY, cameraScale);
          }
        }
        /**
         * Convert world position to gizmo graphics coordinate space
         * For 2D projects, this converts world coordinates to the local space of the graphics node
         */


        worldToGizmoSpace(worldPos, gizmoNode) {
          // Convert world position to local position of the gizmo graphics node
          var localPos = new Vec3();
          gizmoNode.inverseTransformPoint(localPos, worldPos);
          return {
            x: localPos.x,
            y: localPos.y
          };
        }

        drawCenter(graphics, worldX, worldY) {
          if (this.disableCameraScaling) {
            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);
            return;
          }

          try {
            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawCrossScaled(graphics, worldX, worldY, this.centerSize, this.centerColor);
          } catch (error) {
            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);
          }
        }

        drawRadius(graphics, worldX, worldY, radius) {
          if (this.disableCameraScaling) {
            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);
            return;
          }

          try {
            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawCircleScaled(graphics, worldX, worldY, radius, this.radiusColor, false);
          } catch (error) {
            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);
          }
        }

        drawArcIndicator(graphics, emitter, worldX, worldY, cameraScale) {
          if (emitter.arc.value <= 0) return;
          graphics.strokeColor = this.arcColor;
          graphics.lineWidth = 2 * cameraScale; // Convert angle and arc to radians
          // Use the same coordinate system as EmitterArc.getDirection() - no +90 offset

          var baseAngleRad = emitter.angle.value * Math.PI / 180;
          var arcRad = emitter.arc.value * Math.PI / 180;
          var startAngle = baseAngleRad - arcRad / 2;
          var endAngle = baseAngleRad + arcRad / 2; // Draw arc starting from the emitter radius (spawn position) extending outward

          var arcStartRadius = emitter.radius.value; // Start from spawn radius
          // Use same length calculation as direction arrows for consistency

          var baseLength = 30 * cameraScale; // Scale the base length

          var speedFactor = emitter.emitPower.value || 1;
          var arcLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);
          var arcEndRadius = arcStartRadius + arcLength; // Draw arc lines from spawn radius extending outward to show direction range

          var segments = Math.max(8, Math.floor(emitter.arc.value / 5)); // More segments for larger arcs
          // Draw the arc at the end radius to show the direction spread

          for (var i = 0; i <= segments; i++) {
            var angle = startAngle + (endAngle - startAngle) * (i / segments);
            var x = worldX + Math.cos(angle) * arcEndRadius;
            var y = worldY + Math.sin(angle) * arcEndRadius;

            if (i === 0) {
              graphics.moveTo(x, y);
            } else {
              graphics.lineTo(x, y);
            }
          } // Draw lines from spawn position to end of arc to show the direction range


          var startSpawnX = worldX + Math.cos(startAngle) * arcStartRadius;
          var startSpawnY = worldY + Math.sin(startAngle) * arcStartRadius;
          var endSpawnX = worldX + Math.cos(endAngle) * arcStartRadius;
          var endSpawnY = worldY + Math.sin(endAngle) * arcStartRadius;
          var startEndX = worldX + Math.cos(startAngle) * arcEndRadius;
          var startEndY = worldY + Math.sin(startAngle) * arcEndRadius;
          var endEndX = worldX + Math.cos(endAngle) * arcEndRadius;
          var endEndY = worldY + Math.sin(endAngle) * arcEndRadius; // Draw lines from spawn radius to end radius for arc boundaries

          graphics.moveTo(startSpawnX, startSpawnY);
          graphics.lineTo(startEndX, startEndY);
          graphics.moveTo(endSpawnX, endSpawnY);
          graphics.lineTo(endEndX, endEndY);
          graphics.stroke();
        }

        drawDirections(graphics, emitter, worldX, worldY, cameraScale) {
          var baseLength = 30 * cameraScale; // Scale the base length

          var speedFactor = emitter.emitPower.value || 1;
          var arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

          for (var i = 0; i < emitter.count.value; i++) {
            var direction = emitter.getSpawnDirection(i);
            var spawnPos = emitter.getSpawnPosition(i, 0); // Start position (at spawn position relative to world position)

            var startX = worldX + spawnPos.x;
            var startY = worldY + spawnPos.y; // End position (direction from spawn position)

            var endX = startX + direction.x * arrowLength;
            var endY = startY + direction.y * arrowLength; // Draw arrow with scaled size

            (_crd && GizmoUtils === void 0 ? (_reportPossibleCrUseOfGizmoUtils({
              error: Error()
            }), GizmoUtils) : GizmoUtils).drawArrowScaled(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);
          }
        }

        getPriority() {
          return 10; // Draw emitter gizmos with medium priority
        }
        /**
         * Configure display options
         */


        configure(options) {
          if (options.showRadius !== undefined) this.showRadius = options.showRadius;
          if (options.showDirections !== undefined) this.showDirections = options.showDirections;
          if (options.showCenter !== undefined) this.showCenter = options.showCenter;
          if (options.showArc !== undefined) this.showArc = options.showArc;
          if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;
          if (options.directionColor !== undefined) this.directionColor = options.directionColor;
          if (options.centerColor !== undefined) this.centerColor = options.centerColor;
          if (options.arcColor !== undefined) this.arcColor = options.arcColor;
          if (options.speedScale !== undefined) this.speedScale = options.speedScale;
          if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;
          if (options.centerSize !== undefined) this.centerSize = options.centerSize;
          if (options.disableCameraScaling !== undefined) this.disableCameraScaling = options.disableCameraScaling;
        }
        /**
         * Quick method to disable camera scaling for troubleshooting
         * Call this from the console: EmitterGizmo.disableAllCameraScaling()
         */


        static disableAllCameraScaling() {
          try {
            var {
              GizmoManager
            } = require('./GizmoManager');

            var drawer = GizmoManager.getDrawer("EmitterGizmo");

            if (drawer) {
              drawer.disableCameraScaling = true;
            }
          } catch (error) {// Silent fallback
          }
        }
        /**
         * Quick method to re-enable camera scaling
         * Call this from the console: EmitterGizmo.enableAllCameraScaling()
         */


        static enableAllCameraScaling() {
          try {
            var {
              GizmoManager
            } = require('./GizmoManager');

            var drawer = GizmoManager.getDrawer("EmitterGizmo");

            if (drawer) {
              drawer.disableCameraScaling = false;
            }
          } catch (error) {// Silent fallback
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f23230ccff8b04b1bc654c116c9dae559244ed5c.js.map