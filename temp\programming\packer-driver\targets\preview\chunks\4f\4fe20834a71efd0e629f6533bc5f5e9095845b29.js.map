{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/BulletController.ts"], "names": ["BulletProperty", "_decorator", "Component", "Sprite", "Color", "EDITOR", "ObjectPool", "Movable", "eSpriteDefaultFacing", "BulletSystem", "EventGroupContext", "PropertyContainer", "ccclass", "property", "executeInEditMode", "constructor", "duration", "delayDestroy", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "color", "defaultFacing", "isDestroyOutScreen", "isDestructive", "isDestructiveOnHit", "isFacingMoveDir", "isTrackingTarget", "addProperty", "WHITE", "Up", "resetFromData", "data", "value", "eval", "copyFrom", "other", "forEachProperty", "k", "prop", "getPropertyValue", "clear", "BulletController", "type", "displayName", "isRunning", "elapsedTime", "emitter", "bulletData", "eventGroups", "onLoad", "mover", "getComponent", "addComponent", "onBecomeInvisible", "onDestroyBullet", "on", "node", "setScale", "bulletSprite", "onCreate", "resetProperties", "bulletProp", "notifyAll", "resetEventGroups", "eventGroupData", "length", "ctx", "bullet", "eventName", "createBulletEventGroup", "tick", "dt", "destroySelf", "for<PERSON>ach", "group", "stop", "cb", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "returnNode", "scheduleOnce", "onCollisionEnter", "self"], "mappings": ";;;4NAWaA,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXJC,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;;AAC3CC,MAAAA,M,UAAAA,M;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,oB,iBAAAA,oB;;AACTC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAEb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2Cb,U;;gCAEpCD,c,GAAN,MAAMA,cAAN;AAAA;AAAA,kDAAuD;AAgBP;AAEnDe,QAAAA,WAAW,GAAG;AACV;AADU,eAjBPC,QAiBO;AAjBqC;AAiBrC,eAhBPC,YAgBO;AAhBqC;AAgBrC,eAdPC,KAcO;AAdqC;AAcrC,eAbPC,UAaO;AAbqC;AAarC,eAZPC,YAYO;AAZqC;AAYrC,eAXPC,iBAWO;AAXqC;AAWrC,eAVPC,KAUO;AAVqC;AAUrC,eATPC,KASO;AATqC;AASrC,eARPC,aAQO;AARkD;AAQlD,eANPC,kBAMO;AANqC;AAMrC,eALPC,aAKO;AALqC;AAKrC,eAJPC,kBAIO;AAJqC;AAIrC,eAHPC,eAGO;AAHqC;AAGrC,eAFPC,gBAEO;AAEV,eAAKb,QAAL,GAAgB,KAAKc,WAAL,CAAiB,CAAjB,EAAoB,IAApB,CAAhB;AACA,eAAKb,YAAL,GAAoB,KAAKa,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAApB;AACA,eAAKZ,KAAL,GAAa,KAAKY,WAAL,CAAiB,CAAjB,EAAoB,GAApB,CAAb;AACA,eAAKX,UAAL,GAAkB,KAAKW,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAlB;AACA,eAAKV,YAAL,GAAoB,KAAKU,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAApB;AACA,eAAKT,iBAAL,GAAyB,KAAKS,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAzB;AACA,eAAKR,KAAL,GAAa,KAAKQ,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAb;AACA,eAAKP,KAAL,GAAa,KAAKO,WAAL,CAAiB,CAAjB,EAAoB1B,KAAK,CAAC2B,KAA1B,CAAb;AACA,eAAKP,aAAL,GAAqB,KAAKM,WAAL,CAAuC,CAAvC,EAA0C;AAAA;AAAA,4DAAqBE,EAA/D,CAArB;AACA,eAAKP,kBAAL,GAA0B,KAAKK,WAAL,CAAiB,CAAjB,EAAoB,IAApB,CAA1B;AACA,eAAKJ,aAAL,GAAqB,KAAKI,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAArB;AACA,eAAKH,kBAAL,GAA0B,KAAKG,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAA1B;AACA,eAAKF,eAAL,GAAuB,KAAKE,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAAvB;AACA,eAAKD,gBAAL,GAAwB,KAAKC,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAAxB;AACH;;AAEMG,QAAAA,aAAa,CAACC,IAAD,EAAmB;AACnC,eAAKlB,QAAL,CAAcmB,KAAd,GAAsBD,IAAI,CAAClB,QAAL,CAAcoB,IAAd,EAAtB;AACA,eAAKnB,YAAL,CAAkBkB,KAAlB,GAA0BD,IAAI,CAACjB,YAAL,CAAkBmB,IAAlB,EAA1B;AACA,eAAKlB,KAAL,CAAWiB,KAAX,GAAmBD,IAAI,CAAChB,KAAL,CAAWkB,IAAX,EAAnB,CAHmC,CAInC;;AACA,eAAKhB,YAAL,CAAkBe,KAAlB,GAA0BD,IAAI,CAACd,YAAL,CAAkBgB,IAAlB,EAA1B;AACA,eAAKf,iBAAL,CAAuBc,KAAvB,GAA+BD,IAAI,CAACb,iBAAL,CAAuBe,IAAvB,EAA/B;AACA,eAAKd,KAAL,CAAWa,KAAX,GAAmBD,IAAI,CAACZ,KAAL,CAAWc,IAAX,EAAnB,CAPmC,CAQnC;;AACA,eAAKX,kBAAL,CAAwBU,KAAxB,GAAgCD,IAAI,CAACT,kBAArC;AACA,eAAKC,aAAL,CAAmBS,KAAnB,GAA2BD,IAAI,CAACR,aAAhC;AACA,eAAKC,kBAAL,CAAwBQ,KAAxB,GAAgCD,IAAI,CAACP,kBAArC;AACA,eAAKC,eAAL,CAAqBO,KAArB,GAA6BD,IAAI,CAACN,eAAlC;AACA,eAAKC,gBAAL,CAAsBM,KAAtB,GAA8BD,IAAI,CAACL,gBAAnC;AACH;;AAEMQ,QAAAA,QAAQ,CAACC,KAAD,EAAwB;AACnC,eAAKC,eAAL,CAAqB,CAACC,CAAD,EAAIC,IAAJ,KAAa;AAC9BA,YAAAA,IAAI,CAACN,KAAL,GAAaG,KAAK,CAACI,gBAAN,CAAuBF,CAAvB,CAAb;AACH,WAFD;AAGH;;AAEMG,QAAAA,KAAK,GAAG;AACX;AACA,eAAKJ,eAAL,CAAqB,CAACC,CAAD,EAAIC,IAAJ,KAAaA,IAAI,CAACE,KAAL,EAAlC;AACH;;AA7DyD,O,GAgE9D;AACA;;;kCAGaC,gB,WAFZhC,OAAO,CAAC,kBAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,UAGbD,QAAQ,CAAC;AAACgC,QAAAA,IAAI;AAAA;AAAA,8BAAL;AAAgBC,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAIRjC,QAAQ,CAAC;AAACgC,QAAAA,IAAI,EAAE1C,MAAP;AAAe2C,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,4CARb,MAEaF,gBAFb,SAEsC1C,SAFtC,CAEgD;AAAA;AAAA;;AAAA;;AAK5C;AAL4C;;AAAA,eASrC6C,SATqC,GAShB,KATgB;AAAA,eAUrCC,WAVqC,GAUf,CAVe;AAAA,eAYrCC,OAZqC;AAAA,eAarCC,UAbqC;AAe5C;AAf4C,eAgBrCT,IAhBqC,GAgBd,IAAIzC,cAAJ,EAhBc;AAAA,eAkBrCmD,WAlBqC,GAkBT,EAlBS;AAAA;;AAoB5CC,QAAAA,MAAM,GAAS;AACX,cAAI,CAAC,KAAKC,KAAV,EAAiB;AAAA;;AACb,iBAAKA,KAAL,yBAAa,KAAKC,YAAL;AAAA;AAAA,mCAAb,qBAAa,mBAA4BC,YAA5B;AAAA;AAAA,mCAAb;AACH;;AACD,eAAKF,KAAL,CAAWG,iBAAX,GAA+B,MAAM;AACjC,gBAAI,KAAKf,IAAL,CAAUhB,kBAAV,CAA6BU,KAAjC,EAAwC;AACpC;AAAA;AAAA,gDAAasB,eAAb,CAA6B,IAA7B;AACH;AACJ,WAJD;;AAMA,eAAKhB,IAAL,CAAUjB,aAAV,CAAwBW,KAAxB,GAAgC,KAAKkB,KAAL,CAAW7B,aAA3C,CAVW,CAWX;;AACA,eAAKiB,IAAL,CAAUb,eAAV,CAA0B8B,EAA1B,CAA8BvB,KAAD,IAAW;AACpC,iBAAKkB,KAAL,CAAWzB,eAAX,GAA6BO,KAA7B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUZ,gBAAV,CAA2B6B,EAA3B,CAA+BvB,KAAD,IAAW;AACrC,iBAAKkB,KAAL,CAAWxB,gBAAX,GAA8BM,KAA9B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUvB,KAAV,CAAgBwC,EAAhB,CAAoBvB,KAAD,IAAW;AAC1B,iBAAKkB,KAAL,CAAWnC,KAAX,GAAmBiB,KAAnB;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUtB,UAAV,CAAqBuC,EAArB,CAAyBvB,KAAD,IAAW;AAC/B,iBAAKkB,KAAL,CAAWlC,UAAX,GAAwBgB,KAAxB;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUrB,YAAV,CAAuBsC,EAAvB,CAA2BvB,KAAD,IAAW;AACjC,iBAAKkB,KAAL,CAAWjC,YAAX,GAA0Be,KAA1B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUpB,iBAAV,CAA4BqC,EAA5B,CAAgCvB,KAAD,IAAW;AACtC,iBAAKkB,KAAL,CAAWhC,iBAAX,GAA+Bc,KAA/B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUnB,KAAV,CAAgBoC,EAAhB,CAAoBvB,KAAD,IAAW;AAC1B,iBAAKwB,IAAL,CAAUC,QAAV,CAAmBzB,KAAnB,EAA0BA,KAA1B,EAAiCA,KAAjC;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUlB,KAAV,CAAgBmC,EAAhB,CAAoBvB,KAAD,IAAW;AAC1B,gBAAI,KAAK0B,YAAT,EAAuB;AACnB,mBAAKA,YAAL,CAAkBtC,KAAlB,GAA0BY,KAA1B;AACH;AACJ,WAJD;AAKH;;AAEM2B,QAAAA,QAAQ,CAACb,OAAD,EAAyB;AACpC,eAAKF,SAAL,GAAiB,IAAjB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,OAAL,GAAeA,OAAf;AACA,eAAKC,UAAL,GAAkBD,OAAO,CAACC,UAA1B;AAEA,eAAKa,eAAL;AACH;;AAEMA,QAAAA,eAAe,GAAS;AAC3B,cAAI,CAAC,KAAKd,OAAV,EAAmB;AAEnB,eAAKR,IAAL,CAAUJ,QAAV,CAAmB,KAAKY,OAAL,CAAae,UAAhC;AACA,eAAKvB,IAAL,CAAUwB,SAAV,CAAoB,IAApB;AACH;;AAEMC,QAAAA,gBAAgB,GAAS;AAC5B;AACA,cAAI,KAAKhB,UAAL,IAAmB,KAAKA,UAAL,CAAgBiB,cAAhB,CAA+BC,MAA/B,GAAwC,CAA/D,EAAkE;AAC9D,gBAAIC,GAAG,GAAG;AAAA;AAAA,yDAAV;AACAA,YAAAA,GAAG,CAACC,MAAJ,GAAa,IAAb;AACAD,YAAAA,GAAG,CAACpB,OAAJ,GAAc,KAAKA,OAAnB;;AACA,iBAAK,IAAMsB,SAAX,IAAwB,KAAKrB,UAAL,CAAgBiB,cAAxC,EAAwD;AACpD;AAAA;AAAA,gDAAaK,sBAAb,CAAoCH,GAApC,EAAyCE,SAAzC;AACH;AACJ;AACJ;;AAEME,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAK3B,SAAV,EAAqB;AAErB,eAAKC,WAAL,IAAoB0B,EAApB;;AACA,cAAI,KAAK1B,WAAL,GAAmB,KAAKP,IAAL,CAAUzB,QAAV,CAAmBmB,KAA1C,EAAiD;AAC7C,iBAAKwC,WAAL;AACA;AACH,WAPyB,CAS1B;;;AACA,eAAKlC,IAAL,CAAUwB,SAAV;AACH;;AAEMU,QAAAA,WAAW,GAAS;AACvB,eAAK5B,SAAL,GAAiB,KAAjB;;AACA,cAAI,KAAKI,WAAL,IAAoB,KAAKA,WAAL,CAAiBiB,MAAjB,GAA0B,CAAlD,EACA;AACI,iBAAKjB,WAAL,CAAiByB,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACC,IAAN,EAAlC,EADJ,CACqD;;AACjD,iBAAK3B,WAAL,GAAmB,EAAnB,CAFJ,CAE2B;AAC1B;;AACD,cAAM4B,EAAE,GAAG,MAAM;AACb,gBAAI,CAAC,KAAKpB,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUqB,OAA7B,EAAsC;;AAEtC,gBAAI3E,MAAJ,EAAY;AACR,mBAAKsD,IAAL,CAAUsB,OAAV;AACH,aAFD,MAEO;AACH;AAAA;AAAA,4CAAWC,UAAX,CAAsB,KAAKvB,IAA3B;AACH;AACJ,WARD;;AASA,cAAI,KAAKlB,IAAL,CAAUxB,YAAV,IAA0B,KAAKwB,IAAL,CAAUxB,YAAV,CAAuBkB,KAAvB,GAA+B,CAA7D,EAAgE;AAC5D,iBAAKgD,YAAL,CAAkB,MAAM;AACpBJ,cAAAA,EAAE;AACL,aAFD,EAEG,KAAKtC,IAAL,CAAUxB,YAAV,CAAuBkB,KAF1B;AAGH,WAJD,MAIO;AACH4C,YAAAA,EAAE;AACL;AACJ;AAED;AACJ;AACA;;;AACIK,QAAAA,gBAAgB,CAAC9C,KAAD,EAAc+C,IAAd,EAAgC;AAC5C;AACA;AACA;AAAA;AAAA,4CAAa5B,eAAb,CAA6B,IAA7B;AACH;;AArI2C,O;;;;;;;;;;iBAOT,I", "sourcesContent": ["import { _decorator, misc, Component, Node, Sprite, Color, CCString } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Movable, eSpriteDefaultFacing } from '../move/Movable';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from './EventGroup';\r\nimport { Property, PropertyContainer } from './PropertyContainer';\r\nimport { Emitter } from './Emitter';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nexport class BulletProperty extends PropertyContainer<number> {\r\n    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)\r\n    public delayDestroy!: Property<number>;            // 延迟销毁时间\r\n\r\n    public speed!: Property<number>;                   // 子弹速度\r\n    public speedAngle!: Property<number>;              // 子弹速度角度\r\n    public acceleration!: Property<number>;            // 子弹加速度\r\n    public accelerationAngle!: Property<number>;       // 子弹加速度角度\r\n    public scale!: Property<number>;                   // 子弹缩放\r\n    public color!: Property<Color>;                    // 子弹颜色\r\n    public defaultFacing!: Property<eSpriteDefaultFacing>;          // 子弹初始朝向\r\n\r\n    public isDestroyOutScreen!: Property<boolean>;     // 是否超出屏幕销毁\r\n    public isDestructive!: Property<boolean>;          // 是否可被破坏\r\n    public isDestructiveOnHit!: Property<boolean>;     // 命中时是否被销毁\r\n    public isFacingMoveDir!: Property<boolean>;        // 是否面向移动方向\r\n    public isTrackingTarget!: Property<boolean>;       // 是否追踪目标\r\n\r\n    constructor() {\r\n        super();\r\n        this.duration = this.addProperty(0, 6000);\r\n        this.delayDestroy = this.addProperty(1, 0);\r\n        this.speed = this.addProperty(2, 600);\r\n        this.speedAngle = this.addProperty(3, 0);\r\n        this.acceleration = this.addProperty(4, 0);\r\n        this.accelerationAngle = this.addProperty(5, 0);\r\n        this.scale = this.addProperty(6, 1);\r\n        this.color = this.addProperty(7, Color.WHITE);\r\n        this.defaultFacing = this.addProperty<eSpriteDefaultFacing>(8, eSpriteDefaultFacing.Up);\r\n        this.isDestroyOutScreen = this.addProperty(9, true);\r\n        this.isDestructive = this.addProperty(10, false);\r\n        this.isDestructiveOnHit = this.addProperty(11, false);\r\n        this.isFacingMoveDir = this.addProperty(12, false);\r\n        this.isTrackingTarget = this.addProperty(13, false);\r\n    }\r\n\r\n    public resetFromData(data: BulletData) {\r\n        this.duration.value = data.duration.eval(); \r\n        this.delayDestroy.value = data.delayDestroy.eval(); \r\n        this.speed.value = data.speed.eval(); \r\n        // this.speedAngle.value = data.speedAngle.eval(); \r\n        this.acceleration.value = data.acceleration.eval(); \r\n        this.accelerationAngle.value = data.accelerationAngle.eval(); \r\n        this.scale.value = data.scale.eval(); \r\n        // this.color.value = data.color.eval(); \r\n        this.isDestroyOutScreen.value = data.isDestroyOutScreen; \r\n        this.isDestructive.value = data.isDestructive; \r\n        this.isDestructiveOnHit.value = data.isDestructiveOnHit; \r\n        this.isFacingMoveDir.value = data.isFacingMoveDir; \r\n        this.isTrackingTarget.value = data.isTrackingTarget;\r\n    }\r\n\r\n    public copyFrom(other: BulletProperty) {\r\n        this.forEachProperty((k, prop) => {\r\n            prop.value = other.getPropertyValue(k)!;\r\n        });\r\n    }\r\n\r\n    public clear() {\r\n        // Clear all listeners\r\n        this.forEachProperty((k, prop) => prop.clear());\r\n    }\r\n}\r\n\r\n// 子弹 Bullet\r\n// 如何集成到项目里? 考虑把这个类改为BulletController, 控制移动等属性, 作为一个component加入到Bullet:Entity里去\r\n@ccclass('BulletController')\r\n@executeInEditMode(true)\r\nexport class BulletController extends Component {\r\n\r\n    @property({type: Movable, displayName: \"移动组件\"})\r\n    public mover!: Movable;\r\n\r\n    // TODO: 这里后续不处理子弹的sprite显示\r\n    @property({type: Sprite, displayName: \"子弹精灵\"})\r\n    public bulletSprite: Sprite|null = null;\r\n    \r\n    public isRunning: boolean = false;\r\n    public elapsedTime: number = 0;\r\n\r\n    public emitter!: Emitter;\r\n    public bulletData!: BulletData;\r\n\r\n    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里\r\n    public prop: BulletProperty = new BulletProperty();\r\n\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    onLoad(): void {\r\n        if (!this.mover) {\r\n            this.mover = this.getComponent(Movable)?.addComponent(Movable)!;\r\n        }\r\n        this.mover.onBecomeInvisible = () => {\r\n            if (this.prop.isDestroyOutScreen.value) {\r\n                BulletSystem.onDestroyBullet(this);\r\n            }\r\n        };\r\n \r\n        this.prop.defaultFacing.value = this.mover.defaultFacing;\r\n        // listen to property changes\r\n        this.prop.isFacingMoveDir.on((value) => {\r\n            this.mover.isFacingMoveDir = value;\r\n        });\r\n        this.prop.isTrackingTarget.on((value) => {\r\n            this.mover.isTrackingTarget = value;\r\n        });\r\n        this.prop.speed.on((value) => {\r\n            this.mover.speed = value;\r\n        });\r\n        this.prop.speedAngle.on((value) => {\r\n            this.mover.speedAngle = value;\r\n        });\r\n        this.prop.acceleration.on((value) => {\r\n            this.mover.acceleration = value;\r\n        });\r\n        this.prop.accelerationAngle.on((value) => {\r\n            this.mover.accelerationAngle = value;\r\n        });\r\n        this.prop.scale.on((value) => {\r\n            this.node.setScale(value, value, value);\r\n        });\r\n        this.prop.color.on((value) => {\r\n            if (this.bulletSprite) {\r\n                this.bulletSprite.color = value;\r\n            }\r\n        });\r\n    }\r\n\r\n    public onCreate(emitter: Emitter): void {\r\n        this.isRunning = true;\r\n        this.elapsedTime = 0;\r\n        this.emitter = emitter;\r\n        this.bulletData = emitter.bulletData!;\r\n\r\n        this.resetProperties();\r\n    }\r\n\r\n    public resetProperties(): void {\r\n        if (!this.emitter) return;\r\n\r\n        this.prop.copyFrom(this.emitter.bulletProp);\r\n        this.prop.notifyAll(true);\r\n    }\r\n\r\n    public resetEventGroups(): void {\r\n        // create event groups here\r\n        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {\r\n            let ctx = new EventGroupContext();\r\n            ctx.bullet = this;\r\n            ctx.emitter = this.emitter;\r\n            for (const eventName of this.bulletData.eventGroupData) {\r\n                BulletSystem.createBulletEventGroup(ctx, eventName);\r\n            }\r\n        }\r\n    }\r\n\r\n    public tick(dt:number) : void {\r\n        if (!this.isRunning) return;\r\n\r\n        this.elapsedTime += dt;\r\n        if (this.elapsedTime > this.prop.duration.value) {\r\n            this.destroySelf();\r\n            return;\r\n        }\r\n\r\n        // this.mover?.tick(dt);\r\n        this.prop.notifyAll();\r\n    }\r\n\r\n    public destroySelf(): void {\r\n        this.isRunning = false;\r\n        if (this.eventGroups && this.eventGroups.length > 0)\r\n        {\r\n            this.eventGroups.forEach(group => group.stop()); // stop all event groups before destroying the bullet itself.\r\n            this.eventGroups = []; // clear the event groups array\r\n        }\r\n        const cb = () => {\r\n            if (!this.node || !this.node.isValid) return;\r\n            \r\n            if (EDITOR) {\r\n                this.node.destroy();\r\n            } else {\r\n                ObjectPool.returnNode(this.node);\r\n            }\r\n        };\r\n        if (this.prop.delayDestroy && this.prop.delayDestroy.value > 0) {\r\n            this.scheduleOnce(() => {\r\n                cb();\r\n            }, this.prop.delayDestroy.value);\r\n        } else {\r\n            cb();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * TODO: 如果后续自己写碰撞, 这里要相应进行替换\r\n     */\r\n    onCollisionEnter(other: Node, self: Node): void {\r\n        // 判断另一个node也是子弹或者非子弹, 进行相应处理\r\n        // 根据this.isDestructive 和 this.isDestructiveOnHit\r\n        BulletSystem.onDestroyBullet(this);\r\n    }\r\n    \r\n}\r\n"]}